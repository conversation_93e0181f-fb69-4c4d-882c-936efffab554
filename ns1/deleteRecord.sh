#!/bin/bash

input_json="snapProdDomains.json"

REQUEST_TYPE="DELETE"
ZONENAME=""
DOMAINNAME=""
RECORDTYPE="CNAME"

X_NSONE_Key=$(cat X_NSONE_Key.txt)

jq -cr '.[]' $input_json | while read i; do
  ZONENAME=$(echo $i | rev | cut -d "." -f 1-2 | rev)
  DOMAINNAME=$i

  cmd="curl --request $REQUEST_TYPE
    --url https://api.nsone.net/v1/zones/$ZONENAME/$DOMAINNAME/$RECORDTYPE
    --header 'X-NSONE-Key: $X_NSONE_Key'
    --header 'accept: application/json'"

  echo $cmd
  #eval $cmd
done