#!/bin/bash

# Exit on error
set -e

##  curl the fastly api for all the domains and if a tls activation is there it's activated

# Configuration
PAGES=18
PAGE_SIZE=100
OUTPUT_DIR="./activations"

mkdir -p "$OUTPUT_DIR"

# export FASTLY_API_KEY=********************************

FASTLY_API_TOKEN=$(cat FASTLY_API_TOKEN.txt)

if [ -z "$FASTLY_API_TOKEN" ]; then
  echo "Error: FASTLY_API_TOKEN environment variable not set"
  exit 1
fi

# Fetch all pages
for PAGE in $(seq 1 $PAGES); do
  echo "Getting Activations page $PAGE of $PAGES..."

  RESPONSE=$(curl -s \
    -H "Content-Type: application/vnd.api+json" \
    -H "Accept: application/vnd.api+json" \
    -H "Fastly-Key: ${FASTLY_API_TOKEN}" \
    "https://api.fastly.com/tls/activations?page%5Bnumber%5D=$PAGE&page%5Bsize%5D=$PAGE_SIZE")

  # Check for API errors
  if echo "$RESPONSE" | jq -e '.errors' > /dev/null; then
    echo "API Error: $(echo "$RESPONSE" | jq -r '.errors[0].detail')"
    exit 1
  fi

  # Save response to file
  echo "$RESPONSE" | jq . > "$OUTPUT_DIR/activations-$PAGE.json"
done

echo "All TLS activations downloaded successfully to $OUTPUT_DIR/"