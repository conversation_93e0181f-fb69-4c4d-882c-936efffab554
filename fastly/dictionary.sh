#!/bin/bash

#  script to insert keys into a dictionary from vault

DICT=$1
NAME=$2

while IFS= read -r line

do
  IFS=, read -r DICTNAME SVC DICTIONARY <<<"$line"

echo "--Working on $DICTNAME in $NAME-------"
  endpoint="https://api.fastly.com/service/$SVC/dictionary/$DICTIONARY/item"
  command="curl -X POST -H 'Content-Type: application/json' -H 'Fastly-Key: $FASTLY_API_TOKEN' -d @$NAME.json '$endpoint'"
#echo $endpoint
echo $command
eval $command
echo fastly dictionary-entry create --service-id=$SVC --dictionary-id=$DICTIONARY --key=$siteCode --value=$apiKey
sleep 3


done < $DICT