Creating NS1 Record: ci-onboarding.aberdeennews.com
::error::Failed to create record: ci-onboarding.aberdeennews.com
{"message":"record already exists","details":[{"type":"request-id","message":"9498e817-7c6f-4214-8841-e91846fed520"}]}
Creating NS1 Record: ci-onboarding.adelnews.com
::error::Failed to create record: ci-onboarding.adelnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"ee94abd4-7c9f-4d6b-8adb-13cbf36e4f6d"}]}
Creating NS1 Record: ci-onboarding.aledotimesrecord.com
::error::Failed to create record: ci-onboarding.aledotimesrecord.com
{"message":"record already exists","details":[{"type":"request-id","message":"b309042b-c68d-463b-9a5f-1dfcdd54354b"}]}
Creating NS1 Record: ci-onboarding.amarillo.com
::error::Failed to create record: ci-onboarding.amarillo.com
{"message":"record already exists","details":[{"type":"request-id","message":"a3572fe1-aee3-9594-8403-346ef913f319"}]}
Creating NS1 Record: ci-onboarding.amestrib.com
::error::Failed to create record: ci-onboarding.amestrib.com
{"message":"record already exists","details":[{"type":"request-id","message":"6a318b04-83df-42ec-97aa-26a56a6b12cf"}]}
Creating NS1 Record: ci-onboarding.app.com
::error::Failed to create record: ci-onboarding.app.com
{"message":"record already exists","details":[{"type":"request-id","message":"dd363053-ad42-44d6-a3ff-4a20ed73a2e0"}]}
Creating NS1 Record: ci-onboarding.argusleader.com
::error::Failed to create record: ci-onboarding.argusleader.com
{"message":"record already exists","details":[{"type":"request-id","message":"f6585969-f290-430b-bc7c-ebd41c04326d"}]}
Creating NS1 Record: ci-onboarding.augustachronicle.com
::error::Failed to create record: ci-onboarding.augustachronicle.com
{"message":"record already exists","details":[{"type":"request-id","message":"292d1063-0fa1-4403-bfa9-4ed157abf3c7"}]}
Creating NS1 Record: ci-onboarding.austin360.com
::error::Failed to create record: ci-onboarding.austin360.com
{"message":"record already exists","details":[{"type":"request-id","message":"c6be9adb-f949-4d64-9eb4-0b4b399dbe87"}]}
Creating NS1 Record: ci-onboarding.azcentral.com
::error::Failed to create record: ci-onboarding.azcentral.com
{"message":"record already exists","details":[{"type":"request-id","message":"3c8f6263-e317-4260-9a46-357a7ce161ce"}]}
Creating NS1 Record: ci-onboarding.barnesville-enterprise.com
Successfully created record for ci-onboarding.barnesville-enterprise.com
Creating NS1 Record: ci-onboarding.barnstablepatriot.com
::error::Failed to create record: ci-onboarding.barnstablepatriot.com
{"message":"record already exists","details":[{"type":"request-id","message":"2cc063a0-3141-4a0e-9d77-16766cbfe5d1"}]}
Creating NS1 Record: ci-onboarding.battlecreekenquirer.com
::error::Failed to create record: ci-onboarding.battlecreekenquirer.com
{"message":"record already exists","details":[{"type":"request-id","message":"3534cf54-c414-4b43-a0a5-5d7b5ee58fb3"}]}
Creating NS1 Record: ci-onboarding.baystateparent.com
Successfully created record for ci-onboarding.baystateparent.com
Creating NS1 Record: ci-onboarding.beaconjournal.com
::error::Failed to create record: ci-onboarding.beaconjournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"dff5232e-1f38-4979-a4ae-58662198aad2"}]}
Creating NS1 Record: ci-onboarding.blackmountainnews.com
::error::Failed to create record: ci-onboarding.blackmountainnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"6d99ed64-9ece-49c9-b16a-b11ec47f0e46"}]}
Creating NS1 Record: ci-onboarding.blueridgenow.com
::error::Failed to create record: ci-onboarding.blueridgenow.com
{"message":"record already exists","details":[{"type":"request-id","message":"4c7b8fce-9474-4637-a9c2-e0e137647057"}]}
Creating NS1 Record: ci-onboarding.blufftontoday.com
::error::Failed to create record: ci-onboarding.blufftontoday.com
{"message":"record already exists","details":[{"type":"request-id","message":"2cb17ca7-daa0-4b03-868b-4045b2c04f3f"}]}
Creating NS1 Record: ci-onboarding.boonevilledemocrat.com
::error::Failed to create record: ci-onboarding.boonevilledemocrat.com
{"message":"record already exists","details":[{"type":"request-id","message":"8cd49546-fbce-433a-a6fd-42693b6da49b"}]}
Creating NS1 Record: ci-onboarding.buckeyextra.com
::error::Failed to create record: ci-onboarding.buckeyextra.com
{"message":"record already exists","details":[{"type":"request-id","message":"12058cd7-232c-4fa3-8943-e262137f3122"}]}
Creating NS1 Record: ci-onboarding.buckscountycouriertimes.com
::error::Failed to create record: ci-onboarding.buckscountycouriertimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"91fdf3fd-cbfb-4ff6-91c0-4d202b14ef24"}]}
Creating NS1 Record: ci-onboarding.bucyrustelegraphforum.com
::error::Failed to create record: ci-onboarding.bucyrustelegraphforum.com
{"message":"record already exists","details":[{"type":"request-id","message":"c43f1f63-a427-4978-9ccf-c4a7df0e3a2a"}]}
Creating NS1 Record: ci-onboarding.burlingtoncountytimes.com
::error::Failed to create record: ci-onboarding.burlingtoncountytimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"49e57160-2800-436e-94d5-f5ee1abf2c44"}]}
Creating NS1 Record: ci-onboarding.burlingtonfreepress.com
::error::Failed to create record: ci-onboarding.burlingtonfreepress.com
{"message":"record already exists","details":[{"type":"request-id","message":"2681c584-7ab5-442f-b0d7-a8c8a575a475"}]}
Creating NS1 Record: ci-onboarding.caller.com
::error::Failed to create record: ci-onboarding.caller.com
{"message":"record already exists","details":[{"type":"request-id","message":"f8f72e72-dc93-4d34-b980-fe919e6b85fe"}]}
Creating NS1 Record: ci-onboarding.cantondailyledger.com
::error::Failed to create record: ci-onboarding.cantondailyledger.com
{"message":"record already exists","details":[{"type":"request-id","message":"c581ad38-5884-9cb8-a7df-6ae6f5e9b809"}]}
Creating NS1 Record: ci-onboarding.cantonrep.com
::error::Failed to create record: ci-onboarding.cantonrep.com
{"message":"record already exists","details":[{"type":"request-id","message":"e175ea29-24d6-4ccc-ab4f-0b6c1913a2b9"}]}
Creating NS1 Record: ci-onboarding.capecodtimes.com
::error::Failed to create record: ci-onboarding.capecodtimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"f0892687-3fdd-404e-90d2-5f430c949eae"}]}
Creating NS1 Record: ci-onboarding.charlestonexpress.com
::error::Failed to create record: ci-onboarding.charlestonexpress.com
{"message":"record already exists","details":[{"type":"request-id","message":"b6fadf2a-1521-47bc-860c-021394d1a7e9"}]}
Creating NS1 Record: ci-onboarding.cheboygannews.com
::error::Failed to create record: ci-onboarding.cheboygannews.com
{"message":"record already exists","details":[{"type":"request-id","message":"f47abbb7-1f57-4b9f-aa95-308878b3a64d"}]}
Creating NS1 Record: ci-onboarding.chieftain.com
::error::Failed to create record: ci-onboarding.chieftain.com
{"message":"record already exists","details":[{"type":"request-id","message":"7b4e4438-afe6-4bdc-849e-ef3cb6619e1e"}]}
Creating NS1 Record: ci-onboarding.chillicothegazette.com
::error::Failed to create record: ci-onboarding.chillicothegazette.com
{"message":"record already exists","details":[{"type":"request-id","message":"5507e08e-e625-45ba-8ce3-67d55b093e80"}]}
Creating NS1 Record: ci-onboarding.chillicothetimesbulletin.com
Successfully created record for ci-onboarding.chillicothetimesbulletin.com
Creating NS1 Record: ci-onboarding.cincinnati.com
::error::Failed to create record: ci-onboarding.cincinnati.com
{"message":"record already exists","details":[{"type":"request-id","message":"a6890ab4-a413-44e5-a491-d6c16dd85732"}]}
Creating NS1 Record: ci-onboarding.citizen-times.com
::error::Failed to create record: ci-onboarding.citizen-times.com
{"message":"record already exists","details":[{"type":"request-id","message":"d91406cc-11b5-4b4a-9424-ead75d7dd9b7"}]}
Creating NS1 Record: ci-onboarding.cjonline.com
::error::Failed to create record: ci-onboarding.cjonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"41ce6772-83ef-45f2-8722-d714151e095a"}]}
Creating NS1 Record: ci-onboarding.clarionledger.com
::error::Failed to create record: ci-onboarding.clarionledger.com
{"message":"record already exists","details":[{"type":"request-id","message":"51c0705b-96cb-4dcf-b84c-3a73d1e1eec8"}]}
Creating NS1 Record: ci-onboarding.coloradoan.com
::error::Failed to create record: ci-onboarding.coloradoan.com
{"message":"record already exists","details":[{"type":"request-id","message":"e14033aa-3188-4d48-a841-10242d5d8d4c"}]}
Creating NS1 Record: ci-onboarding.columbiadailyherald.com
::error::Failed to create record: ci-onboarding.columbiadailyherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"66902a2f-9f1c-40f9-8160-0f75105bba46"}]}
Creating NS1 Record: ci-onboarding.columbiatribune.com
::error::Failed to create record: ci-onboarding.columbiatribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"cd6aaab0-6231-4af7-bf37-157bad4c15fb"}]}
Creating NS1 Record: ci-onboarding.columbusalive.com
::error::Failed to create record: ci-onboarding.columbusalive.com
{"message":"record already exists","details":[{"type":"request-id","message":"4628450c-9a52-4538-ad94-1d7eb8d524c7"}]}
Creating NS1 Record: ci-onboarding.columbusceo.com
::error::Failed to create record: ci-onboarding.columbusceo.com
{"message":"record already exists","details":[{"type":"request-id","message":"4cf041e6-223c-421a-bc54-1516e893cc71"}]}
Creating NS1 Record: ci-onboarding.columbusmonthly.com
::error::Failed to create record: ci-onboarding.columbusmonthly.com
{"message":"record already exists","details":[{"type":"request-id","message":"b0aba564-735a-4ab6-8711-6015cc8ad148"}]}
Creating NS1 Record: ci-onboarding.columbusparent.com
::error::Failed to create record: ci-onboarding.columbusparent.com
{"message":"record already exists","details":[{"type":"request-id","message":"243d7259-a526-4914-a9ae-af9042881b4e"}]}
Creating NS1 Record: ci-onboarding.commercialappeal.com
::error::Failed to create record: ci-onboarding.commercialappeal.com
{"message":"record already exists","details":[{"type":"request-id","message":"2919b895-e808-43a0-9b14-958728635994"}]}
Creating NS1 Record: ci-onboarding.coshoctontribune.com
::error::Failed to create record: ci-onboarding.coshoctontribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"f6bce7ce-050a-48e1-959c-4794e73a6965"}]}
Creating NS1 Record: ci-onboarding.courier-journal.com
::error::Failed to create record: ci-onboarding.courier-journal.com
{"message":"record already exists","details":[{"type":"request-id","message":"643629bd-3311-4bea-88cc-97df34bfe84f"}]}
Creating NS1 Record: ci-onboarding.courierpostonline.com
::error::Failed to create record: ci-onboarding.courierpostonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"87f53765-1d07-4939-b8d7-8db08164dc4d"}]}
Creating NS1 Record: ci-onboarding.courierpress.com
::error::Failed to create record: ci-onboarding.courierpress.com
{"message":"record already exists","details":[{"type":"request-id","message":"7f40a7ec-2a09-481b-8b74-31cfdcd228de"}]}
Creating NS1 Record: ci-onboarding.daily-jeff.com
::error::Failed to create record: ci-onboarding.daily-jeff.com
{"message":"record already exists","details":[{"type":"request-id","message":"651b1f20-a8bc-4a88-93ae-3260cf769fb6"}]}
Creating NS1 Record: ci-onboarding.dailyamerican.com
::error::Failed to create record: ci-onboarding.dailyamerican.com
{"message":"record already exists","details":[{"type":"request-id","message":"c47299f9-6596-4796-8dda-5812e6b68239"}]}
Creating NS1 Record: ci-onboarding.dailycomet.com
::error::Failed to create record: ci-onboarding.dailycomet.com
{"message":"record already exists","details":[{"type":"request-id","message":"ca9f5ff9-7a07-4b62-9945-873a7690f9b3"}]}
Creating NS1 Record: ci-onboarding.dailycommercial.com
::error::Failed to create record: ci-onboarding.dailycommercial.com
{"message":"record already exists","details":[{"type":"request-id","message":"d69a1d2a-cc17-4f6d-876a-1d4f607335bb"}]}
Creating NS1 Record: ci-onboarding.dailyrecord.com
::error::Failed to create record: ci-onboarding.dailyrecord.com
{"message":"record already exists","details":[{"type":"request-id","message":"a0ed4675-6ed3-47f8-86ae-fb3c98377c6c"}]}
Creating NS1 Record: ci-onboarding.dailyworld.com
::error::Failed to create record: ci-onboarding.dailyworld.com
{"message":"record already exists","details":[{"type":"request-id","message":"a4aface4-bd52-47c2-919d-7f0ef4ad6052"}]}
Creating NS1 Record: ci-onboarding.dansvilleonline.com
Successfully created record for ci-onboarding.dansvilleonline.com
Creating NS1 Record: ci-onboarding.delawareonline.com
::error::Failed to create record: ci-onboarding.delawareonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"44b2654b-cf85-464c-8854-e9f8b38fdb46"}]}
Creating NS1 Record: ci-onboarding.delmarvanow.com
::error::Failed to create record: ci-onboarding.delmarvanow.com
{"message":"record already exists","details":[{"type":"request-id","message":"21be8dac-a20d-40f6-a229-5d1a7209cab1"}]}
Creating NS1 Record: ci-onboarding.democratandchronicle.com
::error::Failed to create record: ci-onboarding.democratandchronicle.com
{"message":"record already exists","details":[{"type":"request-id","message":"133df3e5-8ea0-4633-a7e0-015798a24063"}]}
Creating NS1 Record: ci-onboarding.desertsun.com
::error::Failed to create record: ci-onboarding.desertsun.com
{"message":"record already exists","details":[{"type":"request-id","message":"85b80bc5-e847-4d74-b8b7-d41678ab7804"}]}
Creating NS1 Record: ci-onboarding.desmoinesregister.com
::error::Failed to create record: ci-onboarding.desmoinesregister.com
{"message":"record already exists","details":[{"type":"request-id","message":"8b256e4d-1af2-4615-ad28-1bb137078f26"}]}
Creating NS1 Record: ci-onboarding.detroitnews.com
::error::Failed to create record: ci-onboarding.detroitnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"b7949df0-da6d-494f-a4e7-92dbb67904d1"}]}
Creating NS1 Record: ci-onboarding.dispatch.com
::error::Failed to create record: ci-onboarding.dispatch.com
{"message":"record already exists","details":[{"type":"request-id","message":"a62cdc6e-4266-4c64-9338-6a3ae138a7db"}]}
Creating NS1 Record: ci-onboarding.dmjuice.com
Successfully created record for ci-onboarding.dmjuice.com
Creating NS1 Record: ci-onboarding.dnj.com
::error::Failed to create record: ci-onboarding.dnj.com
{"message":"record already exists","details":[{"type":"request-id","message":"00badeec-1977-4926-83f3-e821d3d65713"}]}
Creating NS1 Record: ci-onboarding.donaldsonvillechief.com
::error::Failed to create record: ci-onboarding.donaldsonvillechief.com
{"message":"record already exists","details":[{"type":"request-id","message":"4cac6fe9-983b-4159-a85f-0bb8dd5139ef"}]}
Creating NS1 Record: ci-onboarding.doverpost.com
::error::Failed to create record: ci-onboarding.doverpost.com
{"message":"record already exists","details":[{"type":"request-id","message":"51429a70-f572-4f40-a8c6-ca90bb6a56a4"}]}
Creating NS1 Record: ci-onboarding.eastpeoriatimescourier.com
Successfully created record for ci-onboarding.eastpeoriatimescourier.com
Creating NS1 Record: ci-onboarding.echo-pilot.com
::error::Failed to create record: ci-onboarding.echo-pilot.com
{"message":"record already exists","details":[{"type":"request-id","message":"f7876791-2976-4f60-87de-da01f78d0829"}]}
Creating NS1 Record: ci-onboarding.ellwoodcityledger.com
::error::Failed to create record: ci-onboarding.ellwoodcityledger.com
{"message":"record already exists","details":[{"type":"request-id","message":"032e241d-448a-4607-857d-3142376c4745"}]}
Creating NS1 Record: ci-onboarding.elpasotimes.com
::error::Failed to create record: ci-onboarding.elpasotimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"4f9be599-0d5e-4ba9-8e96-2ee2fc5b17e3"}]}
Creating NS1 Record: ci-onboarding.elpasoymas.com
Successfully created record for ci-onboarding.elpasoymas.com
Creating NS1 Record: ci-onboarding.elsoldesalinas.com
Successfully created record for ci-onboarding.elsoldesalinas.com
Creating NS1 Record: ci-onboarding.enterprisenews.com
::error::Failed to create record: ci-onboarding.enterprisenews.com
{"message":"record already exists","details":[{"type":"request-id","message":"00b0c665-c9cf-4cce-be47-bf2a8aa57408"}]}
Creating NS1 Record: ci-onboarding.eveningsun.com
::error::Failed to create record: ci-onboarding.eveningsun.com
{"message":"record already exists","details":[{"type":"request-id","message":"c2ce172d-b27d-4105-bd96-708077dcdf94"}]}
Creating NS1 Record: ci-onboarding.eveningtribune.com
::error::Failed to create record: ci-onboarding.eveningtribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"ab0a020d-ae9c-47f5-84ff-332abf6c53ba"}]}
Creating NS1 Record: ci-onboarding.examiner-enterprise.com
::error::Failed to create record: ci-onboarding.examiner-enterprise.com
{"message":"record already exists","details":[{"type":"request-id","message":"4af1b844-fc87-45a3-b041-57011cef8da6"}]}
Creating NS1 Record: ci-onboarding.farmersadvance.com
::error::Failed to create record: ci-onboarding.farmersadvance.com
{"message":"record already exists","details":[{"type":"request-id","message":"e31ac68b-27ff-4b7d-a183-612463859c7b"}]}
Creating NS1 Record: ci-onboarding.farmforum.net
::error::Failed to create record: ci-onboarding.farmforum.net
{"message":"record already exists","details":[{"type":"request-id","message":"e163df4b-1f1f-4118-a456-fc4f98259b82"}]}
Creating NS1 Record: ci-onboarding.fayobserver.com
::error::Failed to create record: ci-onboarding.fayobserver.com
{"message":"record already exists","details":[{"type":"request-id","message":"04d17fbf-3cd9-4712-b3c3-221dba606605"}]}
Creating NS1 Record: ci-onboarding.fdlreporter.com
::error::Failed to create record: ci-onboarding.fdlreporter.com
{"message":"record already exists","details":[{"type":"request-id","message":"844de259-90a1-4581-ac47-ae2279f67d9b"}]}
Creating NS1 Record: ci-onboarding.flipsidepa.com
::error::Failed to create record: ci-onboarding.flipsidepa.com
{"message":"record already exists","details":[{"type":"request-id","message":"b6b80403-653e-4708-92ed-0ad02d154fac"}]}
Creating NS1 Record: ci-onboarding.floridatoday.com
::error::Failed to create record: ci-onboarding.floridatoday.com
{"message":"record already exists","details":[{"type":"request-id","message":"aee954aa-26d5-4ded-9c0f-efce1a486d67"}]}
Creating NS1 Record: ci-onboarding.fosters.com
::error::Failed to create record: ci-onboarding.fosters.com
{"message":"record already exists","details":[{"type":"request-id","message":"bcd24658-2952-4434-950a-2975476cdf0c"}]}
Creating NS1 Record: ci-onboarding.freep.com
::error::Failed to create record: ci-onboarding.freep.com
{"message":"record already exists","details":[{"type":"request-id","message":"e6e26302-2c75-40bf-a033-8f653ddf9960"}]}
Creating NS1 Record: ci-onboarding.fsunews.com
::error::Failed to create record: ci-onboarding.fsunews.com
{"message":"record already exists","details":[{"type":"request-id","message":"415e1ea1-7136-4534-9cd8-33004f2ff35f"}]}
Creating NS1 Record: ci-onboarding.gadsdentimes.com
::error::Failed to create record: ci-onboarding.gadsdentimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"d2d7b5b9-9cce-40be-9992-d3892432cb26"}]}
Creating NS1 Record: ci-onboarding.gainesville.com
::error::Failed to create record: ci-onboarding.gainesville.com
{"message":"record already exists","details":[{"type":"request-id","message":"51fab2ec-59c5-4295-927c-5054de228dbd"}]}
Creating NS1 Record: ci-onboarding.galesburg.com
::error::Failed to create record: ci-onboarding.galesburg.com
{"message":"record already exists","details":[{"type":"request-id","message":"f233e878-6119-45f0-a963-73101e27bf7b"}]}
Creating NS1 Record: ci-onboarding.gametimepa.com
::error::Failed to create record: ci-onboarding.gametimepa.com
{"message":"record already exists","details":[{"type":"request-id","message":"56e00144-9bc0-481d-a3c3-7e6548a7c6e2"}]}
Creating NS1 Record: ci-onboarding.gannett.com
Successfully created record for ci-onboarding.gannett.com
Creating NS1 Record: ci-onboarding.gastongazette.com
::error::Failed to create record: ci-onboarding.gastongazette.com
{"message":"record already exists","details":[{"type":"request-id","message":"232d40a5-266b-4a0c-939d-c44d84475211"}]}
Creating NS1 Record: ci-onboarding.gatorsports.com
::error::Failed to create record: ci-onboarding.gatorsports.com
{"message":"record already exists","details":[{"type":"request-id","message":"c3108809-09ad-475e-9f2d-6d802d54a9e2"}]}
Creating NS1 Record: ci-onboarding.geneseorepublic.com
::error::Failed to create record: ci-onboarding.geneseorepublic.com
{"message":"record already exists","details":[{"type":"request-id","message":"5b52b11f-0ac9-46fd-a428-cbb88463857b"}]}
Creating NS1 Record: ci-onboarding.goerie.com
::error::Failed to create record: ci-onboarding.goerie.com
{"message":"record already exists","details":[{"type":"request-id","message":"cf3cc2bc-0ea7-4dcd-96b1-0b55f46b404c"}]}
Creating NS1 Record: ci-onboarding.gosanangelo.com
::error::Failed to create record: ci-onboarding.gosanangelo.com
{"message":"record already exists","details":[{"type":"request-id","message":"9262cad1-1d98-47f9-accb-0720dbbcd680"}]}
Creating NS1 Record: ci-onboarding.goupstate.com
::error::Failed to create record: ci-onboarding.goupstate.com
{"message":"record already exists","details":[{"type":"request-id","message":"42893dde-6696-4a13-94cb-9f84de6ea6a6"}]}
Creating NS1 Record: ci-onboarding.greatfallstribune.com
::error::Failed to create record: ci-onboarding.greatfallstribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"86037bcf-37c0-4ecc-ae51-3b19effc00ab"}]}
Creating NS1 Record: ci-onboarding.greenbaypressgazette.com
::error::Failed to create record: ci-onboarding.greenbaypressgazette.com
{"message":"record already exists","details":[{"type":"request-id","message":"dac15dfa-ba57-4a97-9a44-351eba9831f5"}]}
Creating NS1 Record: ci-onboarding.greenvilleonline.com
::error::Failed to create record: ci-onboarding.greenvilleonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"11c444b0-ce50-4a1b-a656-9d7a2fe164da"}]}
Creating NS1 Record: ci-onboarding.hattiesburgamerican.com
::error::Failed to create record: ci-onboarding.hattiesburgamerican.com
{"message":"record already exists","details":[{"type":"request-id","message":"8b70a2bc-5b91-4e68-8fbd-0d7d633ab0ed"}]}
Creating NS1 Record: ci-onboarding.hawkcentral.com
::error::Failed to create record: ci-onboarding.hawkcentral.com
{"message":"record already exists","details":[{"type":"request-id","message":"e4e813f1-7ff8-48f1-bce9-940dc29c1aa3"}]}
Creating NS1 Record: ci-onboarding.heraldmailmedia.com
::error::Failed to create record: ci-onboarding.heraldmailmedia.com
{"message":"record already exists","details":[{"type":"request-id","message":"5c6996c9-c68c-4284-9845-e5a68d4409d8"}]}
Creating NS1 Record: ci-onboarding.heraldnews.com
::error::Failed to create record: ci-onboarding.heraldnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"9d7961bb-acd8-4695-9a72-8b407aa3ca18"}]}
Creating NS1 Record: ci-onboarding.heraldtimesonline.com
::error::Failed to create record: ci-onboarding.heraldtimesonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"87a87d00-7bb8-494d-b9c5-0d198e778885"}]}
Creating NS1 Record: ci-onboarding.heraldtribune.com
::error::Failed to create record: ci-onboarding.heraldtribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"1d6f9b8c-83a0-4973-9f03-313aee78e8ac"}]}
Creating NS1 Record: ci-onboarding.hillsdale.net
::error::Failed to create record: ci-onboarding.hillsdale.net
{"message":"record already exists","details":[{"type":"request-id","message":"0c443a1d-1301-4978-b8d3-948782e67154"}]}
Creating NS1 Record: ci-onboarding.hockessincommunitynews.com
::error::Failed to create record: ci-onboarding.hockessincommunitynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"6d48fc82-4193-41ea-8e2f-4d7edbd5a12b"}]}
Creating NS1 Record: ci-onboarding.hollandsentinel.com
::error::Failed to create record: ci-onboarding.hollandsentinel.com
{"message":"record already exists","details":[{"type":"request-id","message":"3ccc52e9-02a8-4540-bb82-9c2638029edd"}]}
Creating NS1 Record: ci-onboarding.hometownlife.com
::error::Failed to create record: ci-onboarding.hometownlife.com
{"message":"record already exists","details":[{"type":"request-id","message":"7d9af863-a90c-4a27-bd91-6e84f1a09ab1"}]}
Creating NS1 Record: ci-onboarding.hookem.com
::error::Failed to create record: ci-onboarding.hookem.com
{"message":"record already exists","details":[{"type":"request-id","message":"c94b2d79-44e2-4ff8-a6a3-f0f9d20c1f8a"}]}
Creating NS1 Record: ci-onboarding.hoopshype.com
Successfully created record for ci-onboarding.hoopshype.com
Creating NS1 Record: ci-onboarding.houmatoday.com
::error::Failed to create record: ci-onboarding.houmatoday.com
{"message":"record already exists","details":[{"type":"request-id","message":"b3ff92e2-bde2-4410-88eb-47731201987d"}]}
Creating NS1 Record: ci-onboarding.htrnews.com
::error::Failed to create record: ci-onboarding.htrnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"1aa5f886-c680-4155-9e1e-42e9c49facae"}]}
Creating NS1 Record: ci-onboarding.hutchnews.com
::error::Failed to create record: ci-onboarding.hutchnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"18a6a967-d5e7-4dc7-813d-a7816f4971d8"}]}
Creating NS1 Record: ci-onboarding.indeonline.com
::error::Failed to create record: ci-onboarding.indeonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"aa8f344b-1349-4523-8ff6-5d6723f18a07"}]}
Creating NS1 Record: ci-onboarding.independentmail.com
::error::Failed to create record: ci-onboarding.independentmail.com
{"message":"record already exists","details":[{"type":"request-id","message":"0fa69866-caf7-4359-80e8-c3b0e82c8932"}]}
Creating NS1 Record: ci-onboarding.indystar.com
::error::Failed to create record: ci-onboarding.indystar.com
{"message":"record already exists","details":[{"type":"request-id","message":"004059bb-b64d-4c3b-9c5e-92cb1d73656b"}]}
Creating NS1 Record: ci-onboarding.inyork.com
Successfully created record for ci-onboarding.inyork.com
Creating NS1 Record: ci-onboarding.ithacajournal.com
::error::Failed to create record: ci-onboarding.ithacajournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"38a71392-961e-41d2-b890-ec5d93542efb"}]}
Creating NS1 Record: ci-onboarding.jacksonsun.com
::error::Failed to create record: ci-onboarding.jacksonsun.com
{"message":"record already exists","details":[{"type":"request-id","message":"b6d0e94b-4441-407a-b637-fb0a4c4a8c8b"}]}
Creating NS1 Record: ci-onboarding.jacksonville.com
::error::Failed to create record: ci-onboarding.jacksonville.com
{"message":"record already exists","details":[{"type":"request-id","message":"6614b9f3-0c1e-4f6e-aa73-a0cb8b6d6bbf"}]}
Creating NS1 Record: ci-onboarding.jconline.com
::error::Failed to create record: ci-onboarding.jconline.com
{"message":"record already exists","details":[{"type":"request-id","message":"355615ef-02a8-48ec-a6f4-c61eedc72598"}]}
Creating NS1 Record: ci-onboarding.jdnews.com
::error::Failed to create record: ci-onboarding.jdnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"0280d91f-bf26-48d9-ae1a-00d72c2e4fb2"}]}
Creating NS1 Record: ci-onboarding.journalstandard.com
::error::Failed to create record: ci-onboarding.journalstandard.com
{"message":"record already exists","details":[{"type":"request-id","message":"f7cf5a5b-7fdb-4328-811a-ca8aa43eeaa7"}]}
Creating NS1 Record: ci-onboarding.jsonline.com
::error::Failed to create record: ci-onboarding.jsonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"82b1ae25-b721-4f42-a61f-b50eafc2ac3a"}]}
Creating NS1 Record: ci-onboarding.kitsapsun.com
::error::Failed to create record: ci-onboarding.kitsapsun.com
{"message":"record already exists","details":[{"type":"request-id","message":"09db4b11-411d-4c45-a26c-d5147a37337c"}]}
Creating NS1 Record: ci-onboarding.knoxnews.com
::error::Failed to create record: ci-onboarding.knoxnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"32a3d910-d2ca-43b4-aa12-e96686751abc"}]}
Creating NS1 Record: ci-onboarding.lancastereaglegazette.com
::error::Failed to create record: ci-onboarding.lancastereaglegazette.com
{"message":"record already exists","details":[{"type":"request-id","message":"dbb61a49-d760-4089-a111-5eb5a7cc0349"}]}
Creating NS1 Record: ci-onboarding.lansingstatejournal.com
::error::Failed to create record: ci-onboarding.lansingstatejournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"c0f532e4-e9bc-41c3-9270-85d0de7fdf8e"}]}
Creating NS1 Record: ci-onboarding.lavozarizona.com
::error::Failed to create record: ci-onboarding.lavozarizona.com
{"message":"record already exists","details":[{"type":"request-id","message":"5fbca420-7559-476f-9a1f-2a0c238c067a"}]}
Creating NS1 Record: ci-onboarding.lcsun-news.com
::error::Failed to create record: ci-onboarding.lcsun-news.com
{"message":"record already exists","details":[{"type":"request-id","message":"f8de3fa5-e50c-4e20-85c3-6a48f8f3adef"}]}
Creating NS1 Record: ci-onboarding.ldnews.com
::error::Failed to create record: ci-onboarding.ldnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"f253c94e-7e2d-43ae-aacf-80c3ea950872"}]}
Creating NS1 Record: ci-onboarding.lenconnect.com
::error::Failed to create record: ci-onboarding.lenconnect.com
{"message":"record already exists","details":[{"type":"request-id","message":"7a1219ae-9828-4b2d-9acf-cafbcf7684ef"}]}
Creating NS1 Record: ci-onboarding.lincolncourier.com
::error::Failed to create record: ci-onboarding.lincolncourier.com
{"message":"record already exists","details":[{"type":"request-id","message":"a014c34f-cac0-45c4-b097-441d2d4e04d5"}]}
Creating NS1 Record: ci-onboarding.linkbostonhomes.com
Successfully created record for ci-onboarding.linkbostonhomes.com
Creating NS1 Record: ci-onboarding.livingstondaily.com
::error::Failed to create record: ci-onboarding.livingstondaily.com
{"message":"record already exists","details":[{"type":"request-id","message":"57f0a6aa-5991-4e2d-872b-71be73761a08"}]}
Creating NS1 Record: ci-onboarding.lohud.com
::error::Failed to create record: ci-onboarding.lohud.com
{"message":"record already exists","details":[{"type":"request-id","message":"82e20f20-c58b-4691-97d5-b825e1b5e572"}]}
Creating NS1 Record: ci-onboarding.lubbockonline.com
::error::Failed to create record: ci-onboarding.lubbockonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"4d674414-8110-4f19-a3ea-0dd9c2ddf103"}]}
Creating NS1 Record: ci-onboarding.mansfieldnewsjournal.com
::error::Failed to create record: ci-onboarding.mansfieldnewsjournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"4e80cf6a-55e9-46a1-b016-005cf34d0c6a"}]}
Creating NS1 Record: ci-onboarding.marconews.com
::error::Failed to create record: ci-onboarding.marconews.com
{"message":"record already exists","details":[{"type":"request-id","message":"d7b5c085-b77b-42cd-b25a-9dafc16013e6"}]}
Creating NS1 Record: ci-onboarding.marionstar.com
::error::Failed to create record: ci-onboarding.marionstar.com
{"message":"record already exists","details":[{"type":"request-id","message":"2a0ce018-37ae-4e01-a916-304913f5350c"}]}
Creating NS1 Record: ci-onboarding.marshfieldnewsherald.com
::error::Failed to create record: ci-onboarding.marshfieldnewsherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"c9cfb10e-461e-44f5-937d-dec20540ea76"}]}
Creating NS1 Record: ci-onboarding.mcdonoughvoice.com
::error::Failed to create record: ci-onboarding.mcdonoughvoice.com
{"message":"record already exists","details":[{"type":"request-id","message":"018d3afd-3852-4d3c-bf81-72037e609e0e"}]}
Creating NS1 Record: ci-onboarding.metrowestdailynews.com
::error::Failed to create record: ci-onboarding.metrowestdailynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"83d33dfe-0f70-4fc0-9b73-4a4caf5c30d6"}]}
Creating NS1 Record: ci-onboarding.middletowntranscript.com
::error::Failed to create record: ci-onboarding.middletowntranscript.com
{"message":"record already exists","details":[{"type":"request-id","message":"07978d75-ae9d-42b2-adbf-c16d05f0631b"}]}
Creating NS1 Record: ci-onboarding.milfordbeacon.com
::error::Failed to create record: ci-onboarding.milfordbeacon.com
{"message":"record already exists","details":[{"type":"request-id","message":"edde7f40-3d3b-47e3-a2f8-4f1f999dd57d"}]}
Creating NS1 Record: ci-onboarding.milforddailynews.com
::error::Failed to create record: ci-onboarding.milforddailynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"6d1b87fc-454e-4c41-9ab6-c4f322ba07ca"}]}
Creating NS1 Record: ci-onboarding.monroecopost.com
Successfully created record for ci-onboarding.monroecopost.com
Creating NS1 Record: ci-onboarding.monroenews.com
::error::Failed to create record: ci-onboarding.monroenews.com
{"message":"record already exists","details":[{"type":"request-id","message":"308f0f56-a3c4-43a7-8926-3441a653980b"}]}
Creating NS1 Record: ci-onboarding.montgomeryadvertiser.com
::error::Failed to create record: ci-onboarding.montgomeryadvertiser.com
{"message":"record already exists","details":[{"type":"request-id","message":"14a52ac1-bb28-4836-b5af-c5e9dac13a66"}]}
Creating NS1 Record: ci-onboarding.mortontimesnews.com
Successfully created record for ci-onboarding.mortontimesnews.com
Creating NS1 Record: ci-onboarding.mpnnow.com
::error::Failed to create record: ci-onboarding.mpnnow.com
{"message":"record already exists","details":[{"type":"request-id","message":"7763ac0a-8165-4e3d-979d-a9608d64e864"}]}
Creating NS1 Record: ci-onboarding.mtshastanews.com
::error::Failed to create record: ci-onboarding.mtshastanews.com
{"message":"record already exists","details":[{"type":"request-id","message":"701a67bc-29fb-4b5e-ad39-67c3e55945c3"}]}
Creating NS1 Record: ci-onboarding.mycentraljersey.com
::error::Failed to create record: ci-onboarding.mycentraljersey.com
{"message":"record already exists","details":[{"type":"request-id","message":"ba807066-ee43-4ca9-adf6-b761e2b74576"}]}
Creating NS1 Record: ci-onboarding.mytownneo.com
::error::Failed to create record: ci-onboarding.mytownneo.com
{"message":"record already exists","details":[{"type":"request-id","message":"c4bed8cf-7942-4f21-bb46-d3e169320364"}]}
Creating NS1 Record: ci-onboarding.naplesnews.com
::error::Failed to create record: ci-onboarding.naplesnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"246da504-cfde-401b-b97c-4bec11770c83"}]}
Creating NS1 Record: ci-onboarding.ndinsider.com
::error::Failed to create record: ci-onboarding.ndinsider.com
{"message":"record already exists","details":[{"type":"request-id","message":"592ad286-5346-4a71-a841-d65dff167f72"}]}
Creating NS1 Record: ci-onboarding.nevadaiowajournal.com
::error::Failed to create record: ci-onboarding.nevadaiowajournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"887fd927-f6dc-990b-9d0c-51c4df77573e"}]}
Creating NS1 Record: ci-onboarding.newarkadvocate.com
::error::Failed to create record: ci-onboarding.newarkadvocate.com
{"message":"record already exists","details":[{"type":"request-id","message":"5110e7a3-a47c-4dcc-bb9b-cb4a53b92f65"}]}
Creating NS1 Record: ci-onboarding.newportri.com
::error::Failed to create record: ci-onboarding.newportri.com
{"message":"record already exists","details":[{"type":"request-id","message":"21f52954-d890-4a62-b614-6f220e38f304"}]}
Creating NS1 Record: ci-onboarding.news-journalonline.com
::error::Failed to create record: ci-onboarding.news-journalonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"401c8c39-5d62-48e3-a287-d2f3f6e42e55"}]}
Creating NS1 Record: ci-onboarding.news-leader.com
::error::Failed to create record: ci-onboarding.news-leader.com
{"message":"record already exists","details":[{"type":"request-id","message":"6be5ac15-d3bd-4d59-932b-2aba66bbeb75"}]}
Creating NS1 Record: ci-onboarding.news-press.com
::error::Failed to create record: ci-onboarding.news-press.com
{"message":"record already exists","details":[{"type":"request-id","message":"a6117ebb-4757-4016-b466-d6f2782615ee"}]}
Creating NS1 Record: ci-onboarding.newschief.com
::error::Failed to create record: ci-onboarding.newschief.com
{"message":"record already exists","details":[{"type":"request-id","message":"7816db4a-7be4-4647-b1d3-273fee7cf03e"}]}
Creating NS1 Record: ci-onboarding.newsherald.com
::error::Failed to create record: ci-onboarding.newsherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"ada519e3-7ffc-4bb6-8c7c-fe008674a5f3"}]}
Creating NS1 Record: ci-onboarding.newsleader.com
::error::Failed to create record: ci-onboarding.newsleader.com
{"message":"record already exists","details":[{"type":"request-id","message":"b7e1e9f7-b262-48ee-bc87-fc7d14c2364a"}]}
Creating NS1 Record: ci-onboarding.newsrepublican.com
::error::Failed to create record: ci-onboarding.newsrepublican.com
{"message":"record already exists","details":[{"type":"request-id","message":"c471d87e-d878-470d-9df6-4e182cb646c7"}]}
Creating NS1 Record: ci-onboarding.njherald.com
::error::Failed to create record: ci-onboarding.njherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"a9252db8-2656-4e74-b2c1-4d67eaf051e1"}]}
Creating NS1 Record: ci-onboarding.northjersey.com
::error::Failed to create record: ci-onboarding.northjersey.com
{"message":"record already exists","details":[{"type":"request-id","message":"6e3477b0-748f-482b-9cc2-ed1c78daaa84"}]}
Creating NS1 Record: ci-onboarding.norwichbulletin.com
::error::Failed to create record: ci-onboarding.norwichbulletin.com
{"message":"record already exists","details":[{"type":"request-id","message":"7bf4f743-d4e2-4034-8085-ef227067a4c6"}]}
Creating NS1 Record: ci-onboarding.nwfdailynews.com
::error::Failed to create record: ci-onboarding.nwfdailynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"5b9459a9-27be-4444-97c5-2750ebc647d6"}]}
Creating NS1 Record: ci-onboarding.oakridger.com
::error::Failed to create record: ci-onboarding.oakridger.com
{"message":"record already exists","details":[{"type":"request-id","message":"9579f853-2f02-485e-a3f0-c49fb9594b5a"}]}
Creating NS1 Record: ci-onboarding.ocala.com
::error::Failed to create record: ci-onboarding.ocala.com
{"message":"record already exists","details":[{"type":"request-id","message":"799e4249-9308-4f3a-87ca-1750458c940c"}]}
Creating NS1 Record: ci-onboarding.oklahoman.com
::error::Failed to create record: ci-onboarding.oklahoman.com
{"message":"record already exists","details":[{"type":"request-id","message":"afe19ff4-e4e6-4c38-96e2-1eb8cbfc9724"}]}
Creating NS1 Record: ci-onboarding.onlineathens.com
::error::Failed to create record: ci-onboarding.onlineathens.com
{"message":"record already exists","details":[{"type":"request-id","message":"dfecf713-a9c6-44f3-b496-4283d6fa09c7"}]}
Creating NS1 Record: ci-onboarding.packersnews.com
::error::Failed to create record: ci-onboarding.packersnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"343a1065-d200-4607-91a5-d57b8c4a6522"}]}
Creating NS1 Record: ci-onboarding.pal-item.com
::error::Failed to create record: ci-onboarding.pal-item.com
{"message":"record already exists","details":[{"type":"request-id","message":"c025c9a7-e5ec-4330-a446-fd30a989aeb6"}]}
Creating NS1 Record: ci-onboarding.palmbeachdailynews.com
::error::Failed to create record: ci-onboarding.palmbeachdailynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"99ca0bce-ada4-447e-beb3-0bfbc66d556f"}]}
Creating NS1 Record: ci-onboarding.palmbeachpost.com
::error::Failed to create record: ci-onboarding.palmbeachpost.com
{"message":"record already exists","details":[{"type":"request-id","message":"3829cf84-ac84-498a-9b68-b940002e71ba"}]}
Creating NS1 Record: ci-onboarding.paris-express.com
::error::Failed to create record: ci-onboarding.paris-express.com
{"message":"record already exists","details":[{"type":"request-id","message":"0b1ca444-24a4-42be-9f3a-a787f1d67eb5"}]}
Creating NS1 Record: ci-onboarding.patriotledger.com
::error::Failed to create record: ci-onboarding.patriotledger.com
{"message":"record already exists","details":[{"type":"request-id","message":"cb8b4852-e22e-4591-8b02-fcde189454fc"}]}
Creating NS1 Record: ci-onboarding.pawhuskajournalcapital.com
::error::Failed to create record: ci-onboarding.pawhuskajournalcapital.com
{"message":"record already exists","details":[{"type":"request-id","message":"5ca8baa0-fe0a-4596-a53f-9ad8fcbef044"}]}
Creating NS1 Record: ci-onboarding.pekintimes.com
::error::Failed to create record: ci-onboarding.pekintimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"e230c720-b51f-4ca6-a620-43b8fd736abc"}]}
Creating NS1 Record: ci-onboarding.petoskeynews.com
::error::Failed to create record: ci-onboarding.petoskeynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"3c5587d0-4929-43e3-be05-4368134f1f91"}]}
Creating NS1 Record: ci-onboarding.phillyburbs.com
::error::Failed to create record: ci-onboarding.phillyburbs.com
{"message":"record already exists","details":[{"type":"request-id","message":"8f860faf-7f96-48d1-9d06-4063a450934b"}]}
Creating NS1 Record: ci-onboarding.pjstar.com
::error::Failed to create record: ci-onboarding.pjstar.com
{"message":"record already exists","details":[{"type":"request-id","message":"8d8b2c56-2166-449e-98b3-cfff61316028"}]}
Creating NS1 Record: ci-onboarding.pnj.com
::error::Failed to create record: ci-onboarding.pnj.com
{"message":"record already exists","details":[{"type":"request-id","message":"3b182383-0e02-4f71-b81f-e59c0d218dbe"}]}
Creating NS1 Record: ci-onboarding.poconorecord.com
::error::Failed to create record: ci-onboarding.poconorecord.com
{"message":"record already exists","details":[{"type":"request-id","message":"fd75df64-c2d5-4c3d-8051-be5aba231927"}]}
Creating NS1 Record: ci-onboarding.pontiacdailyleader.com
::error::Failed to create record: ci-onboarding.pontiacdailyleader.com
{"message":"record already exists","details":[{"type":"request-id","message":"b7904a93-801a-45a0-b31a-9442e832969f"}]}
Creating NS1 Record: ci-onboarding.portclintonnewsherald.com
::error::Failed to create record: ci-onboarding.portclintonnewsherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"d10dd793-2624-43d2-af2a-3108ed2fc919"}]}
Creating NS1 Record: ci-onboarding.postcrescent.com
::error::Failed to create record: ci-onboarding.postcrescent.com
{"message":"record already exists","details":[{"type":"request-id","message":"21f1168c-dc1f-4f00-8414-fe5794ef47cc"}]}
Creating NS1 Record: ci-onboarding.postsouth.com
::error::Failed to create record: ci-onboarding.postsouth.com
{"message":"record already exists","details":[{"type":"request-id","message":"81280f9d-26a8-4ad9-9410-e2b80547dab5"}]}
Creating NS1 Record: ci-onboarding.poughkeepsiejournal.com
::error::Failed to create record: ci-onboarding.poughkeepsiejournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"2f12960c-4ff8-46a3-9c35-78707ca2a8ec"}]}
Creating NS1 Record: ci-onboarding.press-citizen.com
::error::Failed to create record: ci-onboarding.press-citizen.com
{"message":"record already exists","details":[{"type":"request-id","message":"ac051989-8be0-4fa1-a988-5366856ce29f"}]}
Creating NS1 Record: ci-onboarding.pressargus.com
::error::Failed to create record: ci-onboarding.pressargus.com
{"message":"record already exists","details":[{"type":"request-id","message":"9a159332-4b65-4e53-ae3d-e7bc0ecac335"}]}
Creating NS1 Record: ci-onboarding.pressconnects.com
::error::Failed to create record: ci-onboarding.pressconnects.com
{"message":"record already exists","details":[{"type":"request-id","message":"e4713fe2-c61b-4c75-8457-2ea30f8f8f59"}]}
Creating NS1 Record: ci-onboarding.progress-index.com
::error::Failed to create record: ci-onboarding.progress-index.com
{"message":"record already exists","details":[{"type":"request-id","message":"77634fcc-1cd5-477f-9b6b-fb1aa69b2eb3"}]}
Creating NS1 Record: ci-onboarding.providencejournal.com
::error::Failed to create record: ci-onboarding.providencejournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"7f44895b-f280-4c9b-b774-b9f680957698"}]}
Creating NS1 Record: ci-onboarding.publicopiniononline.com
::error::Failed to create record: ci-onboarding.publicopiniononline.com
{"message":"record already exists","details":[{"type":"request-id","message":"c3e4fcce-3314-47fc-8f66-15b1e50cacf7"}]}
Creating NS1 Record: ci-onboarding.record-courier.com
::error::Failed to create record: ci-onboarding.record-courier.com
{"message":"record already exists","details":[{"type":"request-id","message":"ebead6f3-d4a5-445c-826f-78551670054f"}]}
Creating NS1 Record: ci-onboarding.recordnet.com
::error::Failed to create record: ci-onboarding.recordnet.com
{"message":"record already exists","details":[{"type":"request-id","message":"9a79cc2b-0dcd-4bc9-95c4-7a0dbb91f8ca"}]}
Creating NS1 Record: ci-onboarding.recordonline.com
::error::Failed to create record: ci-onboarding.recordonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"db1eadbe-d025-40f4-975d-43b92bbc0f30"}]}
Creating NS1 Record: ci-onboarding.recordstar.com
Successfully created record for ci-onboarding.recordstar.com
Creating NS1 Record: ci-onboarding.redding.com
::error::Failed to create record: ci-onboarding.redding.com
{"message":"record already exists","details":[{"type":"request-id","message":"36b7f308-406f-45dd-98f2-569e9c3f1cbd"}]}
Creating NS1 Record: ci-onboarding.registerguard.com
::error::Failed to create record: ci-onboarding.registerguard.com
{"message":"record already exists","details":[{"type":"request-id","message":"b8059df4-185a-4ddd-ac40-5fe90e8fa07c"}]}
Creating NS1 Record: ci-onboarding.reporter-times.com
::error::Failed to create record: ci-onboarding.reporter-times.com
{"message":"record already exists","details":[{"type":"request-id","message":"128863fa-88ac-45f0-a67c-0559d5cab6af"}]}
Creating NS1 Record: ci-onboarding.reporternews.com
::error::Failed to create record: ci-onboarding.reporternews.com
{"message":"record already exists","details":[{"type":"request-id","message":"55ae4697-f27e-48f0-9310-c57f549f01c2"}]}
Creating NS1 Record: ci-onboarding.reviewatlas.com
::error::Failed to create record: ci-onboarding.reviewatlas.com
{"message":"record already exists","details":[{"type":"request-id","message":"35c4a39a-6521-42df-934e-b114f8023227"}]}
Creating NS1 Record: ci-onboarding.rgj.com
::error::Failed to create record: ci-onboarding.rgj.com
{"message":"record already exists","details":[{"type":"request-id","message":"2fdec1cf-470a-46c9-ae57-7fbcef7c3738"}]}
Creating NS1 Record: ci-onboarding.rrstar.com
::error::Failed to create record: ci-onboarding.rrstar.com
{"message":"record already exists","details":[{"type":"request-id","message":"51c394c0-5e97-48ec-ad47-e1403ea91972"}]}
Creating NS1 Record: ci-onboarding.ruidosonews.com
::error::Failed to create record: ci-onboarding.ruidosonews.com
{"message":"record already exists","details":[{"type":"request-id","message":"c524ead7-d96c-47bc-a906-a6c52f7d2cff"}]}
Creating NS1 Record: ci-onboarding.salina.com
::error::Failed to create record: ci-onboarding.salina.com
{"message":"record already exists","details":[{"type":"request-id","message":"4d47f226-4b76-46c6-ac70-4e19193092ce"}]}
Creating NS1 Record: ci-onboarding.savannahnow.com
::error::Failed to create record: ci-onboarding.savannahnow.com
{"message":"record already exists","details":[{"type":"request-id","message":"9a75c298-6a9b-4cbe-a248-28a9c65d5dd4"}]}
Creating NS1 Record: ci-onboarding.scsuntimes.com
::error::Failed to create record: ci-onboarding.scsuntimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"1006b964-5815-4a89-a007-536b4f75a153"}]}
Creating NS1 Record: ci-onboarding.sctimes.com
::error::Failed to create record: ci-onboarding.sctimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"00aa901b-f38c-48b3-a790-5c7b55537ce4"}]}
Creating NS1 Record: ci-onboarding.seacoastonline.com
::error::Failed to create record: ci-onboarding.seacoastonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"6e1d60e9-a887-4bbd-ba45-7e7f99b2c9f6"}]}
Creating NS1 Record: ci-onboarding.sentinel-standard.com
::error::Failed to create record: ci-onboarding.sentinel-standard.com
{"message":"record already exists","details":[{"type":"request-id","message":"cbe44280-9110-487b-9770-f56377f5a09b"}]}
Creating NS1 Record: ci-onboarding.sheboyganpress.com
::error::Failed to create record: ci-onboarding.sheboyganpress.com
{"message":"record already exists","details":[{"type":"request-id","message":"6255636c-e9e2-461a-9e2f-118699b98205"}]}
Creating NS1 Record: ci-onboarding.shelbystar.com
::error::Failed to create record: ci-onboarding.shelbystar.com
{"message":"record already exists","details":[{"type":"request-id","message":"bf59e437-6d00-420f-aa40-759b87755d8b"}]}
Creating NS1 Record: ci-onboarding.shreveporttimes.com
::error::Failed to create record: ci-onboarding.shreveporttimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"42ef8458-4e9f-4641-9c1e-6799d7cc0bce"}]}
Creating NS1 Record: ci-onboarding.siskiyoudaily.com
::error::Failed to create record: ci-onboarding.siskiyoudaily.com
{"message":"record already exists","details":[{"type":"request-id","message":"5e89d105-c4b2-4bf6-b6e8-d9ab187991fe"}]}
Creating NS1 Record: ci-onboarding.sj-r.com
::error::Failed to create record: ci-onboarding.sj-r.com
{"message":"record already exists","details":[{"type":"request-id","message":"35663c34-5212-495d-9e78-39ff81ddd3a8"}]}
Creating NS1 Record: ci-onboarding.sooeveningnews.com
::error::Failed to create record: ci-onboarding.sooeveningnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"d4d06b80-55ff-41df-b7a7-fca1e9bb3698"}]}
Creating NS1 Record: ci-onboarding.southbendtribune.com
::error::Failed to create record: ci-onboarding.southbendtribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"51a8ed2c-f802-403b-b112-727ab4e426cd"}]}
Creating NS1 Record: ci-onboarding.southcoasttoday.com
::error::Failed to create record: ci-onboarding.southcoasttoday.com
{"message":"record already exists","details":[{"type":"request-id","message":"8b35a939-816c-448d-91d7-9f46394c3c66"}]}
Creating NS1 Record: ci-onboarding.southernkitchen.com
::error::Failed to create record: ci-onboarding.southernkitchen.com
{"message":"record already exists","details":[{"type":"request-id","message":"d6f92f1b-ba02-4c82-bbbd-6037586273d1"}]}
Creating NS1 Record: ci-onboarding.spencereveningworld.com
::error::Failed to create record: ci-onboarding.spencereveningworld.com
{"message":"record already exists","details":[{"type":"request-id","message":"dbfb9744-7364-4da0-bb24-baa932fac91d"}]}
Creating NS1 Record: ci-onboarding.starcourier.com
::error::Failed to create record: ci-onboarding.starcourier.com
{"message":"record already exists","details":[{"type":"request-id","message":"a6d41074-efff-4fc0-9c62-cfaccb33facc"}]}
Creating NS1 Record: ci-onboarding.stargazette.com
::error::Failed to create record: ci-onboarding.stargazette.com
{"message":"record already exists","details":[{"type":"request-id","message":"d9548187-fe28-4194-9585-bdbe9932027d"}]}
Creating NS1 Record: ci-onboarding.starnewsonline.com
::error::Failed to create record: ci-onboarding.starnewsonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"ce460a9e-bb08-4de1-bec3-8e70c6de3353"}]}
Creating NS1 Record: ci-onboarding.statesman.com
::error::Failed to create record: ci-onboarding.statesman.com
{"message":"record already exists","details":[{"type":"request-id","message":"4c9d1502-bcdf-46f0-810a-ad564cc845e6"}]}
Creating NS1 Record: ci-onboarding.statesmanjournal.com
::error::Failed to create record: ci-onboarding.statesmanjournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"c1a8e38d-0337-498a-bc02-3002b4ecfea5"}]}
Creating NS1 Record: ci-onboarding.staugustine.com
::error::Failed to create record: ci-onboarding.staugustine.com
{"message":"record already exists","details":[{"type":"request-id","message":"bbd43473-7243-4702-84b5-02e282f36673"}]}
Creating NS1 Record: ci-onboarding.stevenspointjournal.com
::error::Failed to create record: ci-onboarding.stevenspointjournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"02f0c989-2a74-4e82-9d04-32736310f85a"}]}
Creating NS1 Record: ci-onboarding.storycityherald.com
::error::Failed to create record: ci-onboarding.storycityherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"346f3d76-b7d5-436e-969b-cde2583b7396"}]}
Creating NS1 Record: ci-onboarding.sturgisjournal.com
::error::Failed to create record: ci-onboarding.sturgisjournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"92e02fdd-c8f0-4fef-9e93-bdfe4c2d5a7c"}]}
Creating NS1 Record: ci-onboarding.sussexcountian.com
::error::Failed to create record: ci-onboarding.sussexcountian.com
{"message":"record already exists","details":[{"type":"request-id","message":"bb8e9de5-3910-4479-ae83-84f05c9726f7"}]}
Creating NS1 Record: ci-onboarding.swtimes.com
::error::Failed to create record: ci-onboarding.swtimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"1f8bd120-a8f1-4114-ad5b-d4a84b8450af"}]}
Creating NS1 Record: ci-onboarding.tallahassee.com
::error::Failed to create record: ci-onboarding.tallahassee.com
{"message":"record already exists","details":[{"type":"request-id","message":"ea26a0bd-1269-9625-8e8b-0461dc245744"}]}
Creating NS1 Record: ci-onboarding.tauntongazette.com
::error::Failed to create record: ci-onboarding.tauntongazette.com
{"message":"record already exists","details":[{"type":"request-id","message":"1a800cdb-e1d3-4015-b75d-79096d4f3321"}]}
Creating NS1 Record: ci-onboarding.tcpalm.com
::error::Failed to create record: ci-onboarding.tcpalm.com
{"message":"record already exists","details":[{"type":"request-id","message":"e091169a-15a2-4301-8dba-cf91a05dc21d"}]}
Creating NS1 Record: ci-onboarding.telegram.com
::error::Failed to create record: ci-onboarding.telegram.com
{"message":"record already exists","details":[{"type":"request-id","message":"4d9210d8-e880-4571-8926-a09d916aed59"}]}
Creating NS1 Record: ci-onboarding.tennessean.com
::error::Failed to create record: ci-onboarding.tennessean.com
{"message":"record already exists","details":[{"type":"request-id","message":"4c7d4888-5309-4dc9-880c-c7446160fbad"}]}
Creating NS1 Record: ci-onboarding.the-daily-record.com
::error::Failed to create record: ci-onboarding.the-daily-record.com
{"message":"record already exists","details":[{"type":"request-id","message":"b7272477-c644-4650-9c83-d221026c66a1"}]}
Creating NS1 Record: ci-onboarding.the-leader.com
::error::Failed to create record: ci-onboarding.the-leader.com
{"message":"record already exists","details":[{"type":"request-id","message":"246f0a94-e93b-4078-8c22-f5d956221970"}]}
Creating NS1 Record: ci-onboarding.the-review.com
::error::Failed to create record: ci-onboarding.the-review.com
{"message":"record already exists","details":[{"type":"request-id","message":"c829e16c-9a2b-4c91-9dac-0b13e272edcc"}]}
Creating NS1 Record: ci-onboarding.theadvertiser.com
::error::Failed to create record: ci-onboarding.theadvertiser.com
{"message":"record already exists","details":[{"type":"request-id","message":"910d8b43-1dbb-4073-911d-770ce375b60c"}]}
Creating NS1 Record: ci-onboarding.thecalifornian.com
::error::Failed to create record: ci-onboarding.thecalifornian.com
{"message":"record already exists","details":[{"type":"request-id","message":"7defc600-3cdc-4cb8-af21-f73d2f693faf"}]}
Creating NS1 Record: ci-onboarding.thedailyjournal.com
::error::Failed to create record: ci-onboarding.thedailyjournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"98ea6bdd-e876-4a41-9602-92ee6a77bebf"}]}
Creating NS1 Record: ci-onboarding.thedailyreporter.com
::error::Failed to create record: ci-onboarding.thedailyreporter.com
{"message":"record already exists","details":[{"type":"request-id","message":"36b694cc-14e5-4b0f-af3b-d1155c3e3868"}]}
Creating NS1 Record: ci-onboarding.thedestinlog.com
::error::Failed to create record: ci-onboarding.thedestinlog.com
{"message":"record already exists","details":[{"type":"request-id","message":"713ea1d2-7bff-9572-97d0-8c847d66c350"}]}
Creating NS1 Record: ci-onboarding.thegardnernews.com
::error::Failed to create record: ci-onboarding.thegardnernews.com
{"message":"record already exists","details":[{"type":"request-id","message":"c3d4d73a-0df5-4d20-9b95-2f5c25a77384"}]}
Creating NS1 Record: ci-onboarding.thegleaner.com
::error::Failed to create record: ci-onboarding.thegleaner.com
{"message":"record already exists","details":[{"type":"request-id","message":"833bbb5b-2663-46ad-96db-3541716eeaea"}]}
Creating NS1 Record: ci-onboarding.thehammontonnews.com
Successfully created record for ci-onboarding.thehammontonnews.com
Creating NS1 Record: ci-onboarding.theintell.com
::error::Failed to create record: ci-onboarding.theintell.com
{"message":"record already exists","details":[{"type":"request-id","message":"42246979-9861-4dec-92aa-1ad2b91f1980"}]}
Creating NS1 Record: ci-onboarding.theleafchronicle.com
::error::Failed to create record: ci-onboarding.theleafchronicle.com
{"message":"record already exists","details":[{"type":"request-id","message":"bf27f2d6-4fa7-477f-860b-c5ac0f179042"}]}
Creating NS1 Record: ci-onboarding.theledger.com
::error::Failed to create record: ci-onboarding.theledger.com
{"message":"record already exists","details":[{"type":"request-id","message":"5f02cffc-f394-4ad9-995e-1b5bc63badc6"}]}
Creating NS1 Record: ci-onboarding.thenews-messenger.com
::error::Failed to create record: ci-onboarding.thenews-messenger.com
{"message":"record already exists","details":[{"type":"request-id","message":"0739d93f-864e-47fd-8685-ca49ba142971"}]}
Creating NS1 Record: ci-onboarding.thenewsstar.com
::error::Failed to create record: ci-onboarding.thenewsstar.com
{"message":"record already exists","details":[{"type":"request-id","message":"a915fe81-296a-472a-ab5d-88c9301f25bc"}]}
Creating NS1 Record: ci-onboarding.thenorthwestern.com
::error::Failed to create record: ci-onboarding.thenorthwestern.com
{"message":"record already exists","details":[{"type":"request-id","message":"bf3c3b40-202a-45d4-90eb-9cd92505d5ad"}]}
Creating NS1 Record: ci-onboarding.theperrychief.com
::error::Failed to create record: ci-onboarding.theperrychief.com
{"message":"record already exists","details":[{"type":"request-id","message":"5c56fd90-a375-479b-b4b9-21d28a6005ab"}]}
Creating NS1 Record: ci-onboarding.thepublicopinion.com
::error::Failed to create record: ci-onboarding.thepublicopinion.com
{"message":"record already exists","details":[{"type":"request-id","message":"c25a167b-c021-4367-8592-36f8e28172a6"}]}
Creating NS1 Record: ci-onboarding.therecordherald.com
::error::Failed to create record: ci-onboarding.therecordherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"c47e6702-2650-439f-94b8-e69d0e6f67c6"}]}
Creating NS1 Record: ci-onboarding.thespectrum.com
::error::Failed to create record: ci-onboarding.thespectrum.com
{"message":"record already exists","details":[{"type":"request-id","message":"4c8ddb92-f463-47c1-9b3f-5bc7246468fe"}]}
Creating NS1 Record: ci-onboarding.thestarpress.com
::error::Failed to create record: ci-onboarding.thestarpress.com
{"message":"record already exists","details":[{"type":"request-id","message":"23c72f1d-eb4d-4a83-853e-e82e81f1fc78"}]}
Creating NS1 Record: ci-onboarding.thesuburbanite.com
::error::Failed to create record: ci-onboarding.thesuburbanite.com
{"message":"record already exists","details":[{"type":"request-id","message":"14657c7b-543e-4af0-9260-16af13a4ce77"}]}
Creating NS1 Record: ci-onboarding.thetimesherald.com
::error::Failed to create record: ci-onboarding.thetimesherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"39bc9b52-b55b-45e1-a66a-2afd25593d90"}]}
Creating NS1 Record: ci-onboarding.thetowntalk.com
::error::Failed to create record: ci-onboarding.thetowntalk.com
{"message":"record already exists","details":[{"type":"request-id","message":"7752016a-20c4-4c92-924b-04d13866a85e"}]}
Creating NS1 Record: ci-onboarding.thisweeknews.com
::error::Failed to create record: ci-onboarding.thisweeknews.com
{"message":"record already exists","details":[{"type":"request-id","message":"4d919329-e79a-4d74-929e-a3c84e4eaea7"}]}
Creating NS1 Record: ci-onboarding.tidesports.com
::error::Failed to create record: ci-onboarding.tidesports.com
{"message":"record already exists","details":[{"type":"request-id","message":"579ee7c2-0820-40c9-819f-04a74ab8a2be"}]}
Creating NS1 Record: ci-onboarding.times-gazette.com
::error::Failed to create record: ci-onboarding.times-gazette.com
{"message":"record already exists","details":[{"type":"request-id","message":"f0e36713-10ab-4249-89bf-c44733e5e220"}]}
Creating NS1 Record: ci-onboarding.timesonline.com
::error::Failed to create record: ci-onboarding.timesonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"a10fe95e-d856-4714-94bf-33d6f7251e8b"}]}
Creating NS1 Record: ci-onboarding.timesrecordnews.com
::error::Failed to create record: ci-onboarding.timesrecordnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"76cc16b8-2d0d-4ecc-a748-57b0ff3216b5"}]}
Creating NS1 Record: ci-onboarding.timesreporter.com
::error::Failed to create record: ci-onboarding.timesreporter.com
{"message":"record already exists","details":[{"type":"request-id","message":"f80b6599-7a9e-4960-b06d-0e4772f59164"}]}
Creating NS1 Record: ci-onboarding.timestelegram.com
::error::Failed to create record: ci-onboarding.timestelegram.com
{"message":"record already exists","details":[{"type":"request-id","message":"5606bd0e-f5c3-41f4-a633-2c17184eb38d"}]}
Creating NS1 Record: ci-onboarding.tmnews.com
::error::Failed to create record: ci-onboarding.tmnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"f9978b96-9ee6-4582-b8ed-01e4b3d0e2b3"}]}
Creating NS1 Record: ci-onboarding.tricountyindependent.com
::error::Failed to create record: ci-onboarding.tricountyindependent.com
{"message":"record already exists","details":[{"type":"request-id","message":"5cec973c-1442-445d-9634-5e4831637c52"}]}
Creating NS1 Record: ci-onboarding.tricountytimes.com
::error::Failed to create record: ci-onboarding.tricountytimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"6e4d592c-380f-4e70-a11e-3a66615eafc1"}]}
Creating NS1 Record: ci-onboarding.tucson.com
Successfully created record for ci-onboarding.tucson.com
Creating NS1 Record: ci-onboarding.tuscaloosanews.com
::error::Failed to create record: ci-onboarding.tuscaloosanews.com
{"message":"record already exists","details":[{"type":"request-id","message":"f2c4cd47-2b66-4970-ba14-95db5c4010a4"}]}
Creating NS1 Record: ci-onboarding.upstateparent.com
Successfully created record for ci-onboarding.upstateparent.com
Creating NS1 Record: ci-onboarding.usatoday.com
::error::Failed to create record: ci-onboarding.usatoday.com
{"message":"record already exists","details":[{"type":"request-id","message":"32406415-a015-4bf6-8c9a-8ec7b48364a0"}]}
Creating NS1 Record: ci-onboarding.usatodaynetwork.com
Successfully created record for ci-onboarding.usatodaynetwork.com
Creating NS1 Record: ci-onboarding.usatodaysportsplus.com
Successfully created record for ci-onboarding.usatodaysportsplus.com
Creating NS1 Record: ci-onboarding.uticaod.com
::error::Failed to create record: ci-onboarding.uticaod.com
{"message":"record already exists","details":[{"type":"request-id","message":"5a181d81-54ce-4d75-8799-0ba34de2b460"}]}
Creating NS1 Record: ci-onboarding.vcstar.com
::error::Failed to create record: ci-onboarding.vcstar.com
{"message":"record already exists","details":[{"type":"request-id","message":"75255d8e-3f0b-4ce5-8c0b-95691d581ca9"}]}
Creating NS1 Record: ci-onboarding.visaliatimesdelta.com
::error::Failed to create record: ci-onboarding.visaliatimesdelta.com
{"message":"record already exists","details":[{"type":"request-id","message":"7d7151e3-d706-4471-a9dd-b48b46b67671"}]}
Creating NS1 Record: ci-onboarding.vvdailypress.com
::error::Failed to create record: ci-onboarding.vvdailypress.com
{"message":"record already exists","details":[{"type":"request-id","message":"75c980d3-7828-48d0-9bef-a41f05d86cea"}]}
Creating NS1 Record: ci-onboarding.waltonsun.com
Successfully created record for ci-onboarding.waltonsun.com
Creating NS1 Record: ci-onboarding.washingtontimesreporter.com
Successfully created record for ci-onboarding.washingtontimesreporter.com
Creating NS1 Record: ci-onboarding.wausaudailyherald.com
::error::Failed to create record: ci-onboarding.wausaudailyherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"705698ad-ad27-4dbc-9266-e76b46d70b9c"}]}
Creating NS1 Record: ci-onboarding.waynepost.com
Successfully created record for ci-onboarding.waynepost.com
Creating NS1 Record: ci-onboarding.weeklycitizen.com
::error::Failed to create record: ci-onboarding.weeklycitizen.com
{"message":"record already exists","details":[{"type":"request-id","message":"f2afe7a7-9043-4d25-a096-8f2800260a2c"}]}
Creating NS1 Record: ci-onboarding.wellsvilledaily.com
Successfully created record for ci-onboarding.wellsvilledaily.com
Creating NS1 Record: ci-onboarding.wickedlocal.com
Successfully created record for ci-onboarding.wickedlocal.com
Creating NS1 Record: ci-onboarding.wisconsinrapidstribune.com
::error::Failed to create record: ci-onboarding.wisconsinrapidstribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"606b358f-5719-44f4-a6e0-7489d2b3c611"}]}
Creating NS1 Record: ci-onboarding.wisfarmer.com
::error::Failed to create record: ci-onboarding.wisfarmer.com
{"message":"record already exists","details":[{"type":"request-id","message":"f7c3c723-06d8-4fd4-861c-9cb1383be4df"}]}
Creating NS1 Record: ci-onboarding.woodfordtimes.com
Successfully created record for ci-onboarding.woodfordtimes.com
Creating NS1 Record: ci-onboarding.worcestermag.com
::error::Failed to create record: ci-onboarding.worcestermag.com
{"message":"record already exists","details":[{"type":"request-id","message":"6241a3e2-759e-410e-8f02-4fae8f83e5b4"}]}
Creating NS1 Record: ci-onboarding.ydr.com
::error::Failed to create record: ci-onboarding.ydr.com
{"message":"record already exists","details":[{"type":"request-id","message":"98743421-2976-4b1a-876b-837a2ec015a0"}]}
Creating NS1 Record: ci-onboarding.yorkdispatch.com
::error::Failed to create record: ci-onboarding.yorkdispatch.com
{"message":"record already exists","details":[{"type":"request-id","message":"1507a24a-a50b-46ce-b416-6978d55a3e8a"}]}
Creating NS1 Record: ci-onboarding.zanesvilletimesrecorder.com
::error::Failed to create record: ci-onboarding.zanesvilletimesrecorder.com
{"message":"record already exists","details":[{"type":"request-id","message":"cfd1c9d3-9360-40be-b194-32d7b39039a7"}]}
