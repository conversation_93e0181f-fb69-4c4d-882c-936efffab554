Creating NS1 Record: account-stage.aberdeennews.com
::error::Failed to create record: account-stage.aberdeennews.com
{"message":"record already exists","details":[{"type":"request-id","message":"7686c6a7-62e1-40f3-97ed-e58581e736e3"}]}
Creating NS1 Record: account-stage.adelnews.com
::error::Failed to create record: account-stage.adelnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"b88e27e5-dd52-4b42-b412-eb5db52b2763"}]}
Creating NS1 Record: account-stage.aledotimesrecord.com
::error::Failed to create record: account-stage.aledotimesrecord.com
{"message":"record already exists","details":[{"type":"request-id","message":"a79ca422-3630-42c1-a1d8-3305402f18f0"}]}
Creating NS1 Record: account-stage.amarillo.com
::error::Failed to create record: account-stage.amarillo.com
{"message":"record already exists","details":[{"type":"request-id","message":"78ac0ad5-8c7a-4869-922b-78f98a8591bc"}]}
Creating NS1 Record: account-stage.amestrib.com
::error::Failed to create record: account-stage.amestrib.com
{"message":"record already exists","details":[{"type":"request-id","message":"872aa4cb-10e3-473b-b62b-5d5fca554f13"}]}
Creating NS1 Record: account-stage.app.com
::error::Failed to create record: account-stage.app.com
{"message":"record already exists","details":[{"type":"request-id","message":"a79256d8-1486-4582-8a87-1ed1aedfd6ef"}]}
Creating NS1 Record: account-stage.argusleader.com
::error::Failed to create record: account-stage.argusleader.com
{"message":"record already exists","details":[{"type":"request-id","message":"ba150bf8-48b1-4555-8768-1f65777ddc51"}]}
Creating NS1 Record: account-stage.augustachronicle.com
::error::Failed to create record: account-stage.augustachronicle.com
{"message":"record already exists","details":[{"type":"request-id","message":"********-324a-460e-ac35-98cf58476787"}]}
Creating NS1 Record: account-stage.austin360.com
::error::Failed to create record: account-stage.austin360.com
{"message":"record already exists","details":[{"type":"request-id","message":"89aaf88b-011a-4757-b624-e1cc0af6a385"}]}
Creating NS1 Record: account-stage.azcentral.com
::error::Failed to create record: account-stage.azcentral.com
{"message":"record already exists","details":[{"type":"request-id","message":"72a5efb9-09b5-419c-8441-f3b5185d1578"}]}
Creating NS1 Record: account-stage.barnesville-enterprise.com
::error::Failed to create record: account-stage.barnesville-enterprise.com
{"message":"record already exists","details":[{"type":"request-id","message":"a7736c44-5f60-47d7-8e78-55b6f00a684d"}]}
Creating NS1 Record: account-stage.barnstablepatriot.com
::error::Failed to create record: account-stage.barnstablepatriot.com
{"message":"record already exists","details":[{"type":"request-id","message":"4a1af5e6-9a3c-4b4f-8c40-80a8d932db80"}]}
Creating NS1 Record: account-stage.battlecreekenquirer.com
::error::Failed to create record: account-stage.battlecreekenquirer.com
{"message":"record already exists","details":[{"type":"request-id","message":"e04f773f-19e5-46b6-b543-da9d738fbbb2"}]}
Creating NS1 Record: account-stage.baystateparent.com
::error::Failed to create record: account-stage.baystateparent.com
{"message":"record already exists","details":[{"type":"request-id","message":"548bae87-2f84-4622-b5bd-a7a5e93ae4b2"}]}
Creating NS1 Record: account-stage.beaconjournal.com
::error::Failed to create record: account-stage.beaconjournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"3e3aaf67-8488-4ab8-bcac-378e3ce45960"}]}
Creating NS1 Record: account-stage.blackmountainnews.com
::error::Failed to create record: account-stage.blackmountainnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"39144d32-a1ea-479f-88b9-3470458dd8fe"}]}
Creating NS1 Record: account-stage.blueridgenow.com
::error::Failed to create record: account-stage.blueridgenow.com
{"message":"record already exists","details":[{"type":"request-id","message":"de63513c-f390-4ed7-96f5-7d91406f2075"}]}
Creating NS1 Record: account-stage.blufftontoday.com
::error::Failed to create record: account-stage.blufftontoday.com
{"message":"record already exists","details":[{"type":"request-id","message":"2294eabf-9f2f-4d4c-881e-feec45eadad8"}]}
Creating NS1 Record: account-stage.boonevilledemocrat.com
::error::Failed to create record: account-stage.boonevilledemocrat.com
{"message":"record already exists","details":[{"type":"request-id","message":"d05f51f2-ded8-4c5a-a3e4-96483273be20"}]}
Creating NS1 Record: account-stage.buckeyextra.com
::error::Failed to create record: account-stage.buckeyextra.com
{"message":"record already exists","details":[{"type":"request-id","message":"96683b89-dd5d-4b4c-b4ba-b2d21bebb09d"}]}
Creating NS1 Record: account-stage.buckscountycouriertimes.com
::error::Failed to create record: account-stage.buckscountycouriertimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"4d22243f-1fb6-42d6-b516-221b5e92f5f6"}]}
Creating NS1 Record: account-stage.bucyrustelegraphforum.com
::error::Failed to create record: account-stage.bucyrustelegraphforum.com
{"message":"record already exists","details":[{"type":"request-id","message":"0d08204d-02ab-4dd9-8a52-2c5732ee13e3"}]}
Creating NS1 Record: account-stage.burlingtoncountytimes.com
::error::Failed to create record: account-stage.burlingtoncountytimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"c269ffcd-f3b0-49f2-a77d-6603ac29d5bc"}]}
Creating NS1 Record: account-stage.burlingtonfreepress.com
::error::Failed to create record: account-stage.burlingtonfreepress.com
{"message":"record already exists","details":[{"type":"request-id","message":"00cdc672-cd44-46a4-b40d-c257aed2e3d8"}]}
Creating NS1 Record: account-stage.caller.com
::error::Failed to create record: account-stage.caller.com
{"message":"record already exists","details":[{"type":"request-id","message":"4026c586-5ca4-47ec-81c6-5134eba45165"}]}
Creating NS1 Record: account-stage.cantondailyledger.com
::error::Failed to create record: account-stage.cantondailyledger.com
{"message":"record already exists","details":[{"type":"request-id","message":"653d5e27-04d9-444d-b816-ac5b8c1e5dd0"}]}
Creating NS1 Record: account-stage.cantonrep.com
::error::Failed to create record: account-stage.cantonrep.com
{"message":"record already exists","details":[{"type":"request-id","message":"87d246e9-f234-4d6b-9e70-454060ddf626"}]}
Creating NS1 Record: account-stage.capecodtimes.com
::error::Failed to create record: account-stage.capecodtimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"434b407c-e784-416c-bf84-51d9fc76d119"}]}
Creating NS1 Record: account-stage.charlestonexpress.com
::error::Failed to create record: account-stage.charlestonexpress.com
{"message":"record already exists","details":[{"type":"request-id","message":"eee79e77-e8b2-48b6-9d4e-dc458c128647"}]}
Creating NS1 Record: account-stage.cheboygannews.com
::error::Failed to create record: account-stage.cheboygannews.com
{"message":"record already exists","details":[{"type":"request-id","message":"721297e5-d790-4ad0-a116-2c4378a90705"}]}
Creating NS1 Record: account-stage.chieftain.com
::error::Failed to create record: account-stage.chieftain.com
{"message":"record already exists","details":[{"type":"request-id","message":"dc9059a9-e9e3-4325-864d-8e21842fef7b"}]}
Creating NS1 Record: account-stage.chillicothegazette.com
::error::Failed to create record: account-stage.chillicothegazette.com
{"message":"record already exists","details":[{"type":"request-id","message":"5a073e71-b533-4e9f-9ae5-48faae0a8402"}]}
Creating NS1 Record: account-stage.chillicothetimesbulletin.com
::error::Failed to create record: account-stage.chillicothetimesbulletin.com
{"message":"record already exists","details":[{"type":"request-id","message":"995bea09-79cd-417f-a61a-28c9ca6434cd"}]}
Creating NS1 Record: account-stage.cincinnati.com
::error::Failed to create record: account-stage.cincinnati.com
{"message":"record already exists","details":[{"type":"request-id","message":"1903fabe-f9ac-483c-baa2-08f0a736d52a"}]}
Creating NS1 Record: account-stage.citizen-times.com
::error::Failed to create record: account-stage.citizen-times.com
{"message":"record already exists","details":[{"type":"request-id","message":"c1e97895-a4c1-475e-a483-1e4764d04698"}]}
Creating NS1 Record: account-stage.cjonline.com
::error::Failed to create record: account-stage.cjonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"c971404d-2dae-4f30-9bb6-71110ddfc538"}]}
Creating NS1 Record: account-stage.clarionledger.com
::error::Failed to create record: account-stage.clarionledger.com
{"message":"record already exists","details":[{"type":"request-id","message":"cae4ae74-8070-4d68-b757-90ecc651b2ee"}]}
Creating NS1 Record: account-stage.coloradoan.com
::error::Failed to create record: account-stage.coloradoan.com
{"message":"record already exists","details":[{"type":"request-id","message":"ccbca05f-cf17-4875-b085-321319ec8c56"}]}
Creating NS1 Record: account-stage.columbiadailyherald.com
::error::Failed to create record: account-stage.columbiadailyherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"054470ee-c4eb-41d7-9838-a5fde43de0a1"}]}
Creating NS1 Record: account-stage.columbiatribune.com
::error::Failed to create record: account-stage.columbiatribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"c6395631-aef4-4e1a-99ef-bc1e452e0d98"}]}
Creating NS1 Record: account-stage.columbusalive.com
::error::Failed to create record: account-stage.columbusalive.com
{"message":"record already exists","details":[{"type":"request-id","message":"95bae514-7cf8-4c8e-9036-823acccf6e7b"}]}
Creating NS1 Record: account-stage.columbusceo.com
::error::Failed to create record: account-stage.columbusceo.com
{"message":"record already exists","details":[{"type":"request-id","message":"64e8ee04-9ce0-45c5-89f8-8e4a46acd9c7"}]}
Creating NS1 Record: account-stage.columbusmonthly.com
::error::Failed to create record: account-stage.columbusmonthly.com
{"message":"record already exists","details":[{"type":"request-id","message":"47d16957-154a-45d8-a3be-c08245bf88ca"}]}
Creating NS1 Record: account-stage.columbusparent.com
::error::Failed to create record: account-stage.columbusparent.com
{"message":"record already exists","details":[{"type":"request-id","message":"919748d1-4281-4fde-a19f-39da43540bbb"}]}
Creating NS1 Record: account-stage.commercialappeal.com
::error::Failed to create record: account-stage.commercialappeal.com
{"message":"record already exists","details":[{"type":"request-id","message":"c97721e2-c3ef-498f-a2c7-52d50f22d6f5"}]}
Creating NS1 Record: account-stage.coshoctontribune.com
::error::Failed to create record: account-stage.coshoctontribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"7ae296cd-404d-93b1-ab8b-831a77f700a4"}]}
Creating NS1 Record: account-stage.courier-journal.com
::error::Failed to create record: account-stage.courier-journal.com
{"message":"record already exists","details":[{"type":"request-id","message":"cf029861-a5ed-4c3b-bdfd-0533d8c404cf"}]}
Creating NS1 Record: account-stage.courierpostonline.com
::error::Failed to create record: account-stage.courierpostonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"bc11ae42-0b2f-4763-85ee-57c18d421cc6"}]}
Creating NS1 Record: account-stage.courierpress.com
::error::Failed to create record: account-stage.courierpress.com
{"message":"record already exists","details":[{"type":"request-id","message":"dfd06d0d-8479-4282-8d91-2e25b2d75979"}]}
Creating NS1 Record: account-stage.daily-jeff.com
::error::Failed to create record: account-stage.daily-jeff.com
{"message":"record already exists","details":[{"type":"request-id","message":"8115d6bb-6f7e-426e-a104-49c7e0c4c8fd"}]}
Creating NS1 Record: account-stage.dailyamerican.com
::error::Failed to create record: account-stage.dailyamerican.com
{"message":"record already exists","details":[{"type":"request-id","message":"e66e0f48-ffa7-4390-aa17-297f6958e9da"}]}
Creating NS1 Record: account-stage.dailycomet.com
::error::Failed to create record: account-stage.dailycomet.com
{"message":"record already exists","details":[{"type":"request-id","message":"571072ad-e24b-4d49-9bfa-7f69cb00e011"}]}
Creating NS1 Record: account-stage.dailycommercial.com
::error::Failed to create record: account-stage.dailycommercial.com
{"message":"record already exists","details":[{"type":"request-id","message":"cce62256-35f8-4282-a0a9-ee5a81b51ca6"}]}
Creating NS1 Record: account-stage.dailyrecord.com
::error::Failed to create record: account-stage.dailyrecord.com
{"message":"record already exists","details":[{"type":"request-id","message":"939c2118-485f-4e64-bc2f-95742d4a0401"}]}
Creating NS1 Record: account-stage.dailyworld.com
::error::Failed to create record: account-stage.dailyworld.com
{"message":"record already exists","details":[{"type":"request-id","message":"993a3085-6aba-4fac-8a02-47abfc3e6e17"}]}
Creating NS1 Record: account-stage.dansvilleonline.com
::error::Failed to create record: account-stage.dansvilleonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"c23767f0-9253-4e34-a884-86415e696bc8"}]}
Creating NS1 Record: account-stage.delawareonline.com
::error::Failed to create record: account-stage.delawareonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"0c38e029-b409-43d0-8724-49867e9bf6aa"}]}
Creating NS1 Record: account-stage.delmarvanow.com
::error::Failed to create record: account-stage.delmarvanow.com
{"message":"record already exists","details":[{"type":"request-id","message":"bd593e98-7559-473c-bdad-de8c40bbeac0"}]}
Creating NS1 Record: account-stage.democratandchronicle.com
::error::Failed to create record: account-stage.democratandchronicle.com
{"message":"record already exists","details":[{"type":"request-id","message":"dca561de-a4ed-41a1-b95b-a025d6eb0896"}]}
Creating NS1 Record: account-stage.desertsun.com
::error::Failed to create record: account-stage.desertsun.com
{"message":"record already exists","details":[{"type":"request-id","message":"47132f10-0057-463d-bf4f-50dd558f4eac"}]}
Creating NS1 Record: account-stage.desmoinesregister.com
::error::Failed to create record: account-stage.desmoinesregister.com
{"message":"record already exists","details":[{"type":"request-id","message":"609ce95d-c2e6-487f-8a8c-0742c60c7b69"}]}
Creating NS1 Record: account-stage.detroitnews.com
::error::Failed to create record: account-stage.detroitnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"bd9cbb15-5f44-4dc7-af44-24f5a21261f6"}]}
Creating NS1 Record: account-stage.dispatch.com
::error::Failed to create record: account-stage.dispatch.com
{"message":"record already exists","details":[{"type":"request-id","message":"efe3febe-bf57-402c-9e04-d79cfcdd831f"}]}
Creating NS1 Record: account-stage.dmjuice.com
::error::Failed to create record: account-stage.dmjuice.com
{"message":"record already exists","details":[{"type":"request-id","message":"31936d5c-5323-4ecf-917d-750e829d118c"}]}
Creating NS1 Record: account-stage.dnj.com
::error::Failed to create record: account-stage.dnj.com
{"message":"record already exists","details":[{"type":"request-id","message":"6556ca08-4090-4212-9bf1-99236e10f600"}]}
Creating NS1 Record: account-stage.donaldsonvillechief.com
::error::Failed to create record: account-stage.donaldsonvillechief.com
{"message":"record already exists","details":[{"type":"request-id","message":"2fbdaf95-ebdd-48ef-96ea-e4ac3cc2a7df"}]}
Creating NS1 Record: account-stage.doverpost.com
::error::Failed to create record: account-stage.doverpost.com
{"message":"record already exists","details":[{"type":"request-id","message":"5482d5ef-755d-47b0-b8b9-c137316cbe1c"}]}
Creating NS1 Record: account-stage.eastpeoriatimescourier.com
::error::Failed to create record: account-stage.eastpeoriatimescourier.com
{"message":"record already exists","details":[{"type":"request-id","message":"9404c31d-a1a5-99ed-a1fb-5accd717df67"}]}
Creating NS1 Record: account-stage.echo-pilot.com
::error::Failed to create record: account-stage.echo-pilot.com
{"message":"record already exists","details":[{"type":"request-id","message":"02e7efc5-a89a-4e8b-b901-1a97ee9c5ce2"}]}
Creating NS1 Record: account-stage.ellwoodcityledger.com
::error::Failed to create record: account-stage.ellwoodcityledger.com
{"message":"record already exists","details":[{"type":"request-id","message":"642d53d3-dd35-42e9-9c1c-b097a838e575"}]}
Creating NS1 Record: account-stage.elpasotimes.com
::error::Failed to create record: account-stage.elpasotimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"03cc5d00-46dd-4eab-a2db-a576d79ecf9e"}]}
Creating NS1 Record: account-stage.elpasoymas.com
::error::Failed to create record: account-stage.elpasoymas.com
{"message":"record already exists","details":[{"type":"request-id","message":"f588efd7-f4e4-4a82-9ace-f6842fda680b"}]}
Creating NS1 Record: account-stage.elsoldesalinas.com
::error::Failed to create record: account-stage.elsoldesalinas.com
{"message":"record already exists","details":[{"type":"request-id","message":"864e8fb6-17d6-4ed2-b396-6aa7ae63d982"}]}
Creating NS1 Record: account-stage.enterprisenews.com
::error::Failed to create record: account-stage.enterprisenews.com
{"message":"record already exists","details":[{"type":"request-id","message":"7237b4df-b8b6-483f-8fe9-ffcbc196064d"}]}
Creating NS1 Record: account-stage.eveningsun.com
::error::Failed to create record: account-stage.eveningsun.com
{"message":"record already exists","details":[{"type":"request-id","message":"c589fdc8-74f3-4b27-a53c-ece00b8ba07d"}]}
Creating NS1 Record: account-stage.eveningtribune.com
::error::Failed to create record: account-stage.eveningtribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"3f12bc0f-ec1b-43e0-a065-1ad290d5c5af"}]}
Creating NS1 Record: account-stage.examiner-enterprise.com
::error::Failed to create record: account-stage.examiner-enterprise.com
{"message":"record already exists","details":[{"type":"request-id","message":"1e5e7b39-3db7-4e9f-b13b-b40637e0eedd"}]}
Creating NS1 Record: account-stage.farmersadvance.com
::error::Failed to create record: account-stage.farmersadvance.com
{"message":"record already exists","details":[{"type":"request-id","message":"33b6195b-2a2f-4014-9c85-1496dbbd5163"}]}
Creating NS1 Record: account-stage.farmforum.net
::error::Failed to create record: account-stage.farmforum.net
{"message":"record already exists","details":[{"type":"request-id","message":"3bc109b9-cfed-48b6-af32-5463ca25272b"}]}
Creating NS1 Record: account-stage.fayobserver.com
::error::Failed to create record: account-stage.fayobserver.com
{"message":"record already exists","details":[{"type":"request-id","message":"98267c6b-a016-48cd-a46a-ff1f338ae1e5"}]}
Creating NS1 Record: account-stage.fdlreporter.com
::error::Failed to create record: account-stage.fdlreporter.com
{"message":"record already exists","details":[{"type":"request-id","message":"0d8c63b9-a262-4c4d-9797-7715a5e5f2e2"}]}
Creating NS1 Record: account-stage.flipsidepa.com
::error::Failed to create record: account-stage.flipsidepa.com
{"message":"record already exists","details":[{"type":"request-id","message":"61f7f377-7a1f-4587-8ec5-68e7f97915b4"}]}
Creating NS1 Record: account-stage.floridatoday.com
::error::Failed to create record: account-stage.floridatoday.com
{"message":"record already exists","details":[{"type":"request-id","message":"41d7693d-0df3-4500-b14c-1a668886ba93"}]}
Creating NS1 Record: account-stage.fosters.com
::error::Failed to create record: account-stage.fosters.com
{"message":"record already exists","details":[{"type":"request-id","message":"902f2c9c-a0e0-49bd-820d-0d5d2cc694d3"}]}
Creating NS1 Record: account-stage.freep.com
::error::Failed to create record: account-stage.freep.com
{"message":"record already exists","details":[{"type":"request-id","message":"92c2adc7-35a6-46ed-8aeb-a3bad53a652c"}]}
Creating NS1 Record: account-stage.fsunews.com
::error::Failed to create record: account-stage.fsunews.com
{"message":"record already exists","details":[{"type":"request-id","message":"0c9c826e-483a-4a3b-ae54-285ff1f5ef8c"}]}
Creating NS1 Record: account-stage.gadsdentimes.com
::error::Failed to create record: account-stage.gadsdentimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"a8b3359f-5e71-484f-8765-dbd11ca75f1a"}]}
Creating NS1 Record: account-stage.gainesville.com
::error::Failed to create record: account-stage.gainesville.com
{"message":"record already exists","details":[{"type":"request-id","message":"1429da1f-b967-487d-a1d2-8db3b6d096b4"}]}
Creating NS1 Record: account-stage.galesburg.com
::error::Failed to create record: account-stage.galesburg.com
{"message":"record already exists","details":[{"type":"request-id","message":"8daef843-0eef-4a28-9098-4ca4a486759e"}]}
Creating NS1 Record: account-stage.gametimepa.com
::error::Failed to create record: account-stage.gametimepa.com
{"message":"record already exists","details":[{"type":"request-id","message":"290aedfa-4fd9-4148-91a5-6feb5206a2c7"}]}
Creating NS1 Record: account-stage.gannett.com
::error::Failed to create record: account-stage.gannett.com
{"message":"record already exists","details":[{"type":"request-id","message":"83db39f4-bbdb-4292-8a8c-c68f0c6030bf"}]}
Creating NS1 Record: account-stage.gastongazette.com
::error::Failed to create record: account-stage.gastongazette.com
{"message":"record already exists","details":[{"type":"request-id","message":"adee8cec-ae42-4d11-aeec-c94558e09199"}]}
Creating NS1 Record: account-stage.gatorsports.com
::error::Failed to create record: account-stage.gatorsports.com
{"message":"record already exists","details":[{"type":"request-id","message":"6efb547c-8fcc-4182-8722-35543bac67d7"}]}
Creating NS1 Record: account-stage.geneseorepublic.com
::error::Failed to create record: account-stage.geneseorepublic.com
{"message":"record already exists","details":[{"type":"request-id","message":"********-86e8-44f4-a2b4-c060f2ecc5d7"}]}
Creating NS1 Record: account-stage.goerie.com
::error::Failed to create record: account-stage.goerie.com
{"message":"record already exists","details":[{"type":"request-id","message":"fc472991-652e-4c92-834a-4df86cc4f6be"}]}
Creating NS1 Record: account-stage.gosanangelo.com
::error::Failed to create record: account-stage.gosanangelo.com
{"message":"record already exists","details":[{"type":"request-id","message":"224cccd7-f15e-456e-9210-2e188ff7abf3"}]}
Creating NS1 Record: account-stage.goupstate.com
::error::Failed to create record: account-stage.goupstate.com
{"message":"record already exists","details":[{"type":"request-id","message":"dfdb53df-fba8-46c1-a02a-24e440d911a5"}]}
Creating NS1 Record: account-stage.greatfallstribune.com
::error::Failed to create record: account-stage.greatfallstribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"3172ed02-67c1-48cc-bf1e-ee8baf2baded"}]}
Creating NS1 Record: account-stage.greenbaypressgazette.com
::error::Failed to create record: account-stage.greenbaypressgazette.com
{"message":"record already exists","details":[{"type":"request-id","message":"7fd15d01-b16b-4ab8-8898-a3cdfc1fcb8e"}]}
Creating NS1 Record: account-stage.greenvilleonline.com
::error::Failed to create record: account-stage.greenvilleonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"dae555f2-ba71-4a7a-9163-c2687df6bed8"}]}
Creating NS1 Record: account-stage.hattiesburgamerican.com
::error::Failed to create record: account-stage.hattiesburgamerican.com
{"message":"record already exists","details":[{"type":"request-id","message":"2a6f23dc-d98f-4692-948e-64ee7f0c391d"}]}
Creating NS1 Record: account-stage.hawkcentral.com
::error::Failed to create record: account-stage.hawkcentral.com
{"message":"record already exists","details":[{"type":"request-id","message":"3f882818-e953-4a81-a37e-1dc79a83e0b2"}]}
Creating NS1 Record: account-stage.heraldmailmedia.com
::error::Failed to create record: account-stage.heraldmailmedia.com
{"message":"record already exists","details":[{"type":"request-id","message":"37dd1e09-f707-4e21-8e90-13930f9e2d2a"}]}
Creating NS1 Record: account-stage.heraldnews.com
::error::Failed to create record: account-stage.heraldnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"ab5c3cfa-9510-4bce-ba3b-d024314a45ae"}]}
Creating NS1 Record: account-stage.heraldtimesonline.com
::error::Failed to create record: account-stage.heraldtimesonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"51e9e8bf-9237-4ccf-83c3-e13244cb6636"}]}
Creating NS1 Record: account-stage.heraldtribune.com
::error::Failed to create record: account-stage.heraldtribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"917c6c8d-bb09-48d0-b392-bf0e11359621"}]}
Creating NS1 Record: account-stage.hillsdale.net
::error::Failed to create record: account-stage.hillsdale.net
{"message":"record already exists","details":[{"type":"request-id","message":"490ded77-fa1d-42cf-9638-3b2c47ca9b15"}]}
Creating NS1 Record: account-stage.hockessincommunitynews.com
::error::Failed to create record: account-stage.hockessincommunitynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"838c86ad-b4ed-424c-94ec-47d9843f1252"}]}
Creating NS1 Record: account-stage.hollandsentinel.com
::error::Failed to create record: account-stage.hollandsentinel.com
{"message":"record already exists","details":[{"type":"request-id","message":"0d5c0f25-cb39-4521-9ff7-d68c4ac02211"}]}
Creating NS1 Record: account-stage.hometownlife.com
::error::Failed to create record: account-stage.hometownlife.com
{"message":"record already exists","details":[{"type":"request-id","message":"49b8a1d4-81de-4d62-8365-7daa46e56be4"}]}
Creating NS1 Record: account-stage.hookem.com
::error::Failed to create record: account-stage.hookem.com
{"message":"record already exists","details":[{"type":"request-id","message":"3d7d08e3-6e8b-4f19-b79d-f7f394fe2e13"}]}
Creating NS1 Record: account-stage.hoopshype.com
Successfully created record for account-stage.hoopshype.com
Creating NS1 Record: account-stage.houmatoday.com
::error::Failed to create record: account-stage.houmatoday.com
{"message":"record already exists","details":[{"type":"request-id","message":"ee144de7-b395-4d55-952e-************"}]}
Creating NS1 Record: account-stage.htrnews.com
::error::Failed to create record: account-stage.htrnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"17e7cac1-4ee7-4f66-bf7f-8bd08d1ec279"}]}
Creating NS1 Record: account-stage.hutchnews.com
::error::Failed to create record: account-stage.hutchnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"5a97ff4a-45c1-4da1-a6c9-0fe5497bb656"}]}
Creating NS1 Record: account-stage.indeonline.com
::error::Failed to create record: account-stage.indeonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"876f1c14-688e-48a8-a5fe-7139f7829a71"}]}
Creating NS1 Record: account-stage.independentmail.com
::error::Failed to create record: account-stage.independentmail.com
{"message":"record already exists","details":[{"type":"request-id","message":"3896861a-1efd-4c4a-9aef-9c5e362f5244"}]}
Creating NS1 Record: account-stage.indystar.com
::error::Failed to create record: account-stage.indystar.com
{"message":"record already exists","details":[{"type":"request-id","message":"b70e3100-d426-45fa-972b-5e36baa19b09"}]}
Creating NS1 Record: account-stage.inyork.com
::error::Failed to create record: account-stage.inyork.com
{"message":"record already exists","details":[{"type":"request-id","message":"dc217bf2-3aeb-48d6-ac06-9298f0354739"}]}
Creating NS1 Record: account-stage.ithacajournal.com
::error::Failed to create record: account-stage.ithacajournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"783f91cf-bea2-4fa1-81e9-5d1f44b8c6d8"}]}
Creating NS1 Record: account-stage.jacksonsun.com
::error::Failed to create record: account-stage.jacksonsun.com
{"message":"record already exists","details":[{"type":"request-id","message":"fd8d003e-30b2-4ac8-9860-e4761a648ff3"}]}
Creating NS1 Record: account-stage.jacksonville.com
::error::Failed to create record: account-stage.jacksonville.com
{"message":"record already exists","details":[{"type":"request-id","message":"4c0ae883-3f92-40e9-89b7-33bb46396072"}]}
Creating NS1 Record: account-stage.jconline.com
::error::Failed to create record: account-stage.jconline.com
{"message":"record already exists","details":[{"type":"request-id","message":"34beca78-7faa-4087-bbe2-60415718b3cb"}]}
Creating NS1 Record: account-stage.jdnews.com
::error::Failed to create record: account-stage.jdnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"877e5c57-9b84-4393-b465-324222fe5f0d"}]}
Creating NS1 Record: account-stage.journalstandard.com
::error::Failed to create record: account-stage.journalstandard.com
{"message":"record already exists","details":[{"type":"request-id","message":"70dec3f6-4293-41c2-9836-981e64defc1d"}]}
Creating NS1 Record: account-stage.jsonline.com
::error::Failed to create record: account-stage.jsonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"fa163795-8cf2-98c4-92ad-2a898f36bb27"}]}
Creating NS1 Record: account-stage.kitsapsun.com
::error::Failed to create record: account-stage.kitsapsun.com
{"message":"record already exists","details":[{"type":"request-id","message":"7ab35b41-53a0-4f34-a66d-40ad9925cf9d"}]}
Creating NS1 Record: account-stage.knoxnews.com
::error::Failed to create record: account-stage.knoxnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"790128b1-097d-4747-a762-5184ba6b2389"}]}
Creating NS1 Record: account-stage.lancastereaglegazette.com
::error::Failed to create record: account-stage.lancastereaglegazette.com
{"message":"record already exists","details":[{"type":"request-id","message":"********-d506-47b8-963b-a67f835e62b2"}]}
Creating NS1 Record: account-stage.lansingstatejournal.com
::error::Failed to create record: account-stage.lansingstatejournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"7319afe7-3e68-4bcd-a3c8-3304dc4a6398"}]}
Creating NS1 Record: account-stage.lavozarizona.com
::error::Failed to create record: account-stage.lavozarizona.com
{"message":"record already exists","details":[{"type":"request-id","message":"338071ef-a47e-4264-81b7-a2b4d5a87201"}]}
Creating NS1 Record: account-stage.lcsun-news.com
::error::Failed to create record: account-stage.lcsun-news.com
{"message":"record already exists","details":[{"type":"request-id","message":"4bf2cadc-a328-4ae9-bee3-f6699b0b127e"}]}
Creating NS1 Record: account-stage.ldnews.com
::error::Failed to create record: account-stage.ldnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"111c0aad-237b-424d-8500-8d0139cd3978"}]}
Creating NS1 Record: account-stage.lenconnect.com
::error::Failed to create record: account-stage.lenconnect.com
{"message":"record already exists","details":[{"type":"request-id","message":"7d56a99d-e19e-43be-a417-89d358cf0e05"}]}
Creating NS1 Record: account-stage.lincolncourier.com
::error::Failed to create record: account-stage.lincolncourier.com
{"message":"record already exists","details":[{"type":"request-id","message":"1b0b7f8d-5bca-4cca-a7d3-7ccf824d3f0a"}]}
Creating NS1 Record: account-stage.linkbostonhomes.com
::error::Failed to create record: account-stage.linkbostonhomes.com
{"message":"record already exists","details":[{"type":"request-id","message":"f670f56c-2b74-4b06-8eca-987eed91c886"}]}
Creating NS1 Record: account-stage.livingstondaily.com
::error::Failed to create record: account-stage.livingstondaily.com
{"message":"record already exists","details":[{"type":"request-id","message":"09bb9960-6316-41d8-9f54-7b2ffcc0a63a"}]}
Creating NS1 Record: account-stage.lohud.com
::error::Failed to create record: account-stage.lohud.com
{"message":"record already exists","details":[{"type":"request-id","message":"e89b9eaf-1b22-44ad-9ec9-000cfa274e67"}]}
Creating NS1 Record: account-stage.lubbockonline.com
::error::Failed to create record: account-stage.lubbockonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"36b43426-cd32-481c-8935-3c03552dd373"}]}
Creating NS1 Record: account-stage.mansfieldnewsjournal.com
::error::Failed to create record: account-stage.mansfieldnewsjournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"6275eb1e-70fc-4444-84b7-8d82d1134cac"}]}
Creating NS1 Record: account-stage.marconews.com
Successfully created record for account-stage.marconews.com
Creating NS1 Record: account-stage.marionstar.com
::error::Failed to create record: account-stage.marionstar.com
{"message":"record already exists","details":[{"type":"request-id","message":"be7ec535-b582-47c7-a879-9550f0efc8d9"}]}
Creating NS1 Record: account-stage.marshfieldnewsherald.com
::error::Failed to create record: account-stage.marshfieldnewsherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"40eb24bb-c0c2-40d4-899a-287ec2b32914"}]}
Creating NS1 Record: account-stage.mcdonoughvoice.com
::error::Failed to create record: account-stage.mcdonoughvoice.com
{"message":"record already exists","details":[{"type":"request-id","message":"73cc93dd-0f7a-4a1f-a758-38b3ccc6a0b6"}]}
Creating NS1 Record: account-stage.metrowestdailynews.com
::error::Failed to create record: account-stage.metrowestdailynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"4805c0ca-d9f1-45c8-85ea-657158d2cfa9"}]}
Creating NS1 Record: account-stage.middletowntranscript.com
::error::Failed to create record: account-stage.middletowntranscript.com
{"message":"record already exists","details":[{"type":"request-id","message":"1b7f7616-ca88-4e38-b0c8-c62af70f9ee2"}]}
Creating NS1 Record: account-stage.milfordbeacon.com
::error::Failed to create record: account-stage.milfordbeacon.com
{"message":"record already exists","details":[{"type":"request-id","message":"a89b95e7-2df8-4229-9cba-a2fa861938ff"}]}
Creating NS1 Record: account-stage.milforddailynews.com
::error::Failed to create record: account-stage.milforddailynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"64e0964f-c4b9-484d-9873-9cba1c901115"}]}
Creating NS1 Record: account-stage.monroecopost.com
::error::Failed to create record: account-stage.monroecopost.com
{"message":"record already exists","details":[{"type":"request-id","message":"314d39ad-f048-4f5d-a34b-c44ddf62d954"}]}
Creating NS1 Record: account-stage.monroenews.com
::error::Failed to create record: account-stage.monroenews.com
{"message":"record already exists","details":[{"type":"request-id","message":"0ba02349-6e48-4c97-9438-09978c0e6604"}]}
Creating NS1 Record: account-stage.montgomeryadvertiser.com
::error::Failed to create record: account-stage.montgomeryadvertiser.com
{"message":"record already exists","details":[{"type":"request-id","message":"cbbc426a-dd7a-4d2c-9449-8cec1ec1bacf"}]}
Creating NS1 Record: account-stage.mortontimesnews.com
::error::Failed to create record: account-stage.mortontimesnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"73c0ca29-09f2-4e0b-a7fa-102ad34a8c54"}]}
Creating NS1 Record: account-stage.mpnnow.com
::error::Failed to create record: account-stage.mpnnow.com
{"message":"record already exists","details":[{"type":"request-id","message":"229085fe-d61a-49a4-912e-51716f17a060"}]}
Creating NS1 Record: account-stage.mtshastanews.com
::error::Failed to create record: account-stage.mtshastanews.com
{"message":"record already exists","details":[{"type":"request-id","message":"277c912e-5848-4444-b3f2-94860e175277"}]}
Creating NS1 Record: account-stage.mycentraljersey.com
::error::Failed to create record: account-stage.mycentraljersey.com
{"message":"record already exists","details":[{"type":"request-id","message":"c6482f02-eaf5-4173-b936-e795645e5af4"}]}
Creating NS1 Record: account-stage.mytownneo.com
::error::Failed to create record: account-stage.mytownneo.com
{"message":"record already exists","details":[{"type":"request-id","message":"2869d9c5-c80d-4227-8b91-3f13cba67357"}]}
Creating NS1 Record: account-stage.naplesnews.com
::error::Failed to create record: account-stage.naplesnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"541b4e2e-475a-462e-8e90-dc2be16fb31b"}]}
Creating NS1 Record: account-stage.ndinsider.com
::error::Failed to create record: account-stage.ndinsider.com
{"message":"record already exists","details":[{"type":"request-id","message":"db9dffe5-1c42-4cd2-b4c5-133c26f84fdc"}]}
Creating NS1 Record: account-stage.nevadaiowajournal.com
::error::Failed to create record: account-stage.nevadaiowajournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"39906dbc-a8ee-473f-a3e9-318046634d17"}]}
Creating NS1 Record: account-stage.newarkadvocate.com
::error::Failed to create record: account-stage.newarkadvocate.com
{"message":"record already exists","details":[{"type":"request-id","message":"4212fbbb-6339-41c2-bfb0-b8fcdef95bd5"}]}
Creating NS1 Record: account-stage.newportri.com
::error::Failed to create record: account-stage.newportri.com
{"message":"record already exists","details":[{"type":"request-id","message":"83b1f63a-a14f-4661-b52e-9ddde8756cca"}]}
Creating NS1 Record: account-stage.news-journalonline.com
::error::Failed to create record: account-stage.news-journalonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"ff168ead-6c4e-4d57-92a7-1f723181b305"}]}
Creating NS1 Record: account-stage.news-leader.com
::error::Failed to create record: account-stage.news-leader.com
{"message":"record already exists","details":[{"type":"request-id","message":"bae872fb-6bc4-47cb-bd6e-461744f8bfca"}]}
Creating NS1 Record: account-stage.news-press.com
::error::Failed to create record: account-stage.news-press.com
{"message":"record already exists","details":[{"type":"request-id","message":"f8253c99-6685-40ed-8459-f5f5481affa6"}]}
Creating NS1 Record: account-stage.newschief.com
::error::Failed to create record: account-stage.newschief.com
{"message":"record already exists","details":[{"type":"request-id","message":"de251689-e704-433d-9931-8b7f9d76d412"}]}
Creating NS1 Record: account-stage.newsherald.com
::error::Failed to create record: account-stage.newsherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"9b21c5f3-d283-4ebf-af93-d327dda196ec"}]}
Creating NS1 Record: account-stage.newsleader.com
::error::Failed to create record: account-stage.newsleader.com
{"message":"record already exists","details":[{"type":"request-id","message":"69d5c7b5-81c6-4d6c-887a-42233f901072"}]}
Creating NS1 Record: account-stage.newsrepublican.com
::error::Failed to create record: account-stage.newsrepublican.com
{"message":"record already exists","details":[{"type":"request-id","message":"b449a2d5-bfed-4a6a-a176-11ec44ee0d93"}]}
Creating NS1 Record: account-stage.njherald.com
::error::Failed to create record: account-stage.njherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"4cff82d6-6880-42dc-9f74-017e3d64d4be"}]}
Creating NS1 Record: account-stage.northjersey.com
::error::Failed to create record: account-stage.northjersey.com
{"message":"record already exists","details":[{"type":"request-id","message":"64d7974f-a1bf-4ba3-9c65-f8f85ed87005"}]}
Creating NS1 Record: account-stage.norwichbulletin.com
::error::Failed to create record: account-stage.norwichbulletin.com
{"message":"record already exists","details":[{"type":"request-id","message":"4b9de523-1c75-4f10-aa22-df1f9b8bf461"}]}
Creating NS1 Record: account-stage.nwfdailynews.com
::error::Failed to create record: account-stage.nwfdailynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"d4a70507-b706-4725-a3ce-b339ce9f5ab1"}]}
Creating NS1 Record: account-stage.oakridger.com
::error::Failed to create record: account-stage.oakridger.com
{"message":"record already exists","details":[{"type":"request-id","message":"70e1d674-c748-416c-b680-bbdc159b4cae"}]}
Creating NS1 Record: account-stage.ocala.com
::error::Failed to create record: account-stage.ocala.com
{"message":"record already exists","details":[{"type":"request-id","message":"********-c38b-49d9-9757-a56501da71c7"}]}
Creating NS1 Record: account-stage.oklahoman.com
::error::Failed to create record: account-stage.oklahoman.com
{"message":"record already exists","details":[{"type":"request-id","message":"d09bf19b-99a0-411a-bc23-74bae79fde86"}]}
Creating NS1 Record: account-stage.onlineathens.com
::error::Failed to create record: account-stage.onlineathens.com
{"message":"record already exists","details":[{"type":"request-id","message":"9282acc1-c1fc-4952-a6e0-27173a0a1eb4"}]}
Creating NS1 Record: account-stage.packersnews.com
::error::Failed to create record: account-stage.packersnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"45f3a6ca-114c-46ef-b42c-63108c9a7cd8"}]}
Creating NS1 Record: account-stage.pal-item.com
::error::Failed to create record: account-stage.pal-item.com
{"message":"record already exists","details":[{"type":"request-id","message":"b4ab80d4-798f-4b31-bf2b-42e92b97bbcc"}]}
Creating NS1 Record: account-stage.palmbeachdailynews.com
::error::Failed to create record: account-stage.palmbeachdailynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"a37d07d8-469c-46c1-ae48-69713aa5bfce"}]}
Creating NS1 Record: account-stage.palmbeachpost.com
::error::Failed to create record: account-stage.palmbeachpost.com
{"message":"record already exists","details":[{"type":"request-id","message":"3651ee8c-28fc-4455-b9d1-f5fa3e31de6c"}]}
Creating NS1 Record: account-stage.paris-express.com
::error::Failed to create record: account-stage.paris-express.com
{"message":"record already exists","details":[{"type":"request-id","message":"039524b5-3016-4f7b-ae86-90ed2712d6aa"}]}
Creating NS1 Record: account-stage.patriotledger.com
::error::Failed to create record: account-stage.patriotledger.com
{"message":"record already exists","details":[{"type":"request-id","message":"8c87de13-9fa3-4441-8092-774c05a1310f"}]}
Creating NS1 Record: account-stage.pawhuskajournalcapital.com
::error::Failed to create record: account-stage.pawhuskajournalcapital.com
{"message":"record already exists","details":[{"type":"request-id","message":"726abf04-0537-4026-ad89-121b10d200f7"}]}
Creating NS1 Record: account-stage.pekintimes.com
::error::Failed to create record: account-stage.pekintimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"5f03fa46-34e2-4826-bded-25122f213dad"}]}
Creating NS1 Record: account-stage.petoskeynews.com
::error::Failed to create record: account-stage.petoskeynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"9412820c-9211-4cf0-af80-3d47574f9df8"}]}
Creating NS1 Record: account-stage.phillyburbs.com
::error::Failed to create record: account-stage.phillyburbs.com
{"message":"record already exists","details":[{"type":"request-id","message":"4b7b4860-fb4e-4223-8fb8-619ccf4006b3"}]}
Creating NS1 Record: account-stage.pjstar.com
::error::Failed to create record: account-stage.pjstar.com
{"message":"record already exists","details":[{"type":"request-id","message":"971a3430-eb53-49fa-8972-9a63022576c5"}]}
Creating NS1 Record: account-stage.pnj.com
::error::Failed to create record: account-stage.pnj.com
{"message":"record already exists","details":[{"type":"request-id","message":"2df8bbc1-70d7-42ef-8aae-dc7f364b8929"}]}
Creating NS1 Record: account-stage.poconorecord.com
::error::Failed to create record: account-stage.poconorecord.com
{"message":"record already exists","details":[{"type":"request-id","message":"d143d7c2-40c5-4de6-aa05-18131d166d13"}]}
Creating NS1 Record: account-stage.pontiacdailyleader.com
::error::Failed to create record: account-stage.pontiacdailyleader.com
{"message":"record already exists","details":[{"type":"request-id","message":"68cb76fd-5599-4d85-b667-f42daeeaee9a"}]}
Creating NS1 Record: account-stage.portclintonnewsherald.com
::error::Failed to create record: account-stage.portclintonnewsherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"7671d02b-944c-4669-b36f-15f8c0dcc9d8"}]}
Creating NS1 Record: account-stage.postcrescent.com
::error::Failed to create record: account-stage.postcrescent.com
{"message":"record already exists","details":[{"type":"request-id","message":"9c1bedb6-0466-4081-89d4-82439e46eb6e"}]}
Creating NS1 Record: account-stage.postsouth.com
::error::Failed to create record: account-stage.postsouth.com
{"message":"record already exists","details":[{"type":"request-id","message":"deb715ca-b23f-491a-a365-921db4eb0557"}]}
Creating NS1 Record: account-stage.poughkeepsiejournal.com
::error::Failed to create record: account-stage.poughkeepsiejournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"94f5d4d2-14f5-4961-b9b0-d4ad3080859b"}]}
Creating NS1 Record: account-stage.press-citizen.com
::error::Failed to create record: account-stage.press-citizen.com
{"message":"record already exists","details":[{"type":"request-id","message":"290ed4ef-9574-4477-97d7-0987a2b40717"}]}
Creating NS1 Record: account-stage.pressargus.com
::error::Failed to create record: account-stage.pressargus.com
{"message":"record already exists","details":[{"type":"request-id","message":"6d5da862-d2af-4203-9369-cfd9cd259875"}]}
Creating NS1 Record: account-stage.pressconnects.com
::error::Failed to create record: account-stage.pressconnects.com
{"message":"record already exists","details":[{"type":"request-id","message":"4aee296d-a3e5-4f85-88c6-50f4076a2101"}]}
Creating NS1 Record: account-stage.progress-index.com
::error::Failed to create record: account-stage.progress-index.com
{"message":"record already exists","details":[{"type":"request-id","message":"938dec5d-e8df-4e7f-8ac6-dc044eecea45"}]}
Creating NS1 Record: account-stage.providencejournal.com
::error::Failed to create record: account-stage.providencejournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"da247557-4af1-4a9b-820b-4f006f225c37"}]}
Creating NS1 Record: account-stage.publicopiniononline.com
::error::Failed to create record: account-stage.publicopiniononline.com
{"message":"record already exists","details":[{"type":"request-id","message":"13edc623-41f0-420d-9786-79b2c841f9a0"}]}
Creating NS1 Record: account-stage.record-courier.com
::error::Failed to create record: account-stage.record-courier.com
{"message":"record already exists","details":[{"type":"request-id","message":"d3b9f056-5be4-4812-90d6-643e4b4674c1"}]}
Creating NS1 Record: account-stage.recordnet.com
::error::Failed to create record: account-stage.recordnet.com
{"message":"record already exists","details":[{"type":"request-id","message":"2e2ac42a-3196-424c-b4aa-52cec01d1b98"}]}
Creating NS1 Record: account-stage.recordonline.com
::error::Failed to create record: account-stage.recordonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"2bc35253-abbe-43fe-9830-e1e087d6b56b"}]}
Creating NS1 Record: account-stage.recordstar.com
::error::Failed to create record: account-stage.recordstar.com
{"message":"record already exists","details":[{"type":"request-id","message":"fe5b9a4b-d3df-443b-b635-da02eb58ccd6"}]}
Creating NS1 Record: account-stage.redding.com
::error::Failed to create record: account-stage.redding.com
{"message":"record already exists","details":[{"type":"request-id","message":"ccf39898-756b-4c68-a98d-************"}]}
Creating NS1 Record: account-stage.registerguard.com
::error::Failed to create record: account-stage.registerguard.com
{"message":"record already exists","details":[{"type":"request-id","message":"d3e17dcd-bebe-4e8e-872f-5e7ca30ecac5"}]}
Creating NS1 Record: account-stage.reporter-times.com
::error::Failed to create record: account-stage.reporter-times.com
{"message":"record already exists","details":[{"type":"request-id","message":"89c00a8d-9caa-4aac-8794-16bee6e895cd"}]}
Creating NS1 Record: account-stage.reporternews.com
::error::Failed to create record: account-stage.reporternews.com
{"message":"record already exists","details":[{"type":"request-id","message":"2018bcd8-b73d-4047-9458-19f778d98e6c"}]}
Creating NS1 Record: account-stage.reviewatlas.com
::error::Failed to create record: account-stage.reviewatlas.com
{"message":"record already exists","details":[{"type":"request-id","message":"052f2a20-1b4e-4730-ab81-b23d6177302d"}]}
Creating NS1 Record: account-stage.rgj.com
::error::Failed to create record: account-stage.rgj.com
{"message":"record already exists","details":[{"type":"request-id","message":"4c40d424-185b-4b3d-a10d-425737964a26"}]}
Creating NS1 Record: account-stage.rrstar.com
::error::Failed to create record: account-stage.rrstar.com
{"message":"record already exists","details":[{"type":"request-id","message":"badcea8d-6519-4747-a556-b402c62ae37d"}]}
Creating NS1 Record: account-stage.ruidosonews.com
::error::Failed to create record: account-stage.ruidosonews.com
{"message":"record already exists","details":[{"type":"request-id","message":"a1916efc-3e3c-4ce4-af66-251e03057a37"}]}
Creating NS1 Record: account-stage.salina.com
::error::Failed to create record: account-stage.salina.com
{"message":"record already exists","details":[{"type":"request-id","message":"a2b4c5c0-6358-40c8-bd0c-ed9dfc23f8ad"}]}
Creating NS1 Record: account-stage.savannahnow.com
::error::Failed to create record: account-stage.savannahnow.com
{"message":"record already exists","details":[{"type":"request-id","message":"********-aa88-49df-abed-e25ac249475e"}]}
Creating NS1 Record: account-stage.scsuntimes.com
::error::Failed to create record: account-stage.scsuntimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"7c07e63a-22de-4325-a8eb-61c9def6ec1e"}]}
Creating NS1 Record: account-stage.sctimes.com
::error::Failed to create record: account-stage.sctimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"31bb1de4-2deb-484b-817b-36499bd7e40f"}]}
Creating NS1 Record: account-stage.seacoastonline.com
::error::Failed to create record: account-stage.seacoastonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"00d94638-3bb4-45da-9c5c-db943f9c1b05"}]}
Creating NS1 Record: account-stage.sentinel-standard.com
::error::Failed to create record: account-stage.sentinel-standard.com
{"message":"record already exists","details":[{"type":"request-id","message":"c75e5c5d-7e06-915a-86be-9cd9a266be2f"}]}
Creating NS1 Record: account-stage.sheboyganpress.com
::error::Failed to create record: account-stage.sheboyganpress.com
{"message":"record already exists","details":[{"type":"request-id","message":"76f12269-05a8-4375-8ca0-4a353a65059d"}]}
Creating NS1 Record: account-stage.shelbystar.com
::error::Failed to create record: account-stage.shelbystar.com
{"message":"record already exists","details":[{"type":"request-id","message":"f1a3a2c0-e202-4c25-b459-aa74702c261a"}]}
Creating NS1 Record: account-stage.shreveporttimes.com
::error::Failed to create record: account-stage.shreveporttimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"f65961d5-81b4-4fe2-ae7c-aa0123d07e10"}]}
Creating NS1 Record: account-stage.siskiyoudaily.com
::error::Failed to create record: account-stage.siskiyoudaily.com
{"message":"record already exists","details":[{"type":"request-id","message":"6907eafa-941e-44aa-8a34-8c69ac0748e1"}]}
Creating NS1 Record: account-stage.sj-r.com
::error::Failed to create record: account-stage.sj-r.com
{"message":"record already exists","details":[{"type":"request-id","message":"3d06a7c4-ca72-488a-bbe9-752ec4a00a12"}]}
Creating NS1 Record: account-stage.sooeveningnews.com
::error::Failed to create record: account-stage.sooeveningnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"0dc32e09-0777-47b2-b3c1-f702fda12fe7"}]}
Creating NS1 Record: account-stage.southbendtribune.com
::error::Failed to create record: account-stage.southbendtribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"67bda65a-3d10-422c-b433-83a82a97e737"}]}
Creating NS1 Record: account-stage.southcoasttoday.com
::error::Failed to create record: account-stage.southcoasttoday.com
{"message":"record already exists","details":[{"type":"request-id","message":"34f5fc34-fca4-48ac-9b86-ea28922007dc"}]}
Creating NS1 Record: account-stage.southernkitchen.com
::error::Failed to create record: account-stage.southernkitchen.com
{"message":"record already exists","details":[{"type":"request-id","message":"7daf82fc-b5ce-491e-a129-1ce9d9425d7d"}]}
Creating NS1 Record: account-stage.spencereveningworld.com
::error::Failed to create record: account-stage.spencereveningworld.com
{"message":"record already exists","details":[{"type":"request-id","message":"f65c3061-3ac8-4d5c-a6c4-0e9321544700"}]}
Creating NS1 Record: account-stage.starcourier.com
::error::Failed to create record: account-stage.starcourier.com
{"message":"record already exists","details":[{"type":"request-id","message":"dbae5976-7d03-443c-bc9c-61467967ae05"}]}
Creating NS1 Record: account-stage.stargazette.com
::error::Failed to create record: account-stage.stargazette.com
{"message":"record already exists","details":[{"type":"request-id","message":"1b85ee67-488f-4153-897b-468eb6f374c6"}]}
Creating NS1 Record: account-stage.starnewsonline.com
::error::Failed to create record: account-stage.starnewsonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"6d3ae4f7-4a24-4fd8-8641-288b468403e9"}]}
Creating NS1 Record: account-stage.statesman.com
::error::Failed to create record: account-stage.statesman.com
{"message":"record already exists","details":[{"type":"request-id","message":"ad3498ad-4e88-4cac-ab99-3ad3172594b9"}]}
Creating NS1 Record: account-stage.statesmanjournal.com
::error::Failed to create record: account-stage.statesmanjournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"757bd3ee-b43a-484f-b682-4489a84752cf"}]}
Creating NS1 Record: account-stage.staugustine.com
::error::Failed to create record: account-stage.staugustine.com
{"message":"record already exists","details":[{"type":"request-id","message":"c54326c2-24c8-4c7a-a740-b4b10fc8d1a7"}]}
Creating NS1 Record: account-stage.stevenspointjournal.com
::error::Failed to create record: account-stage.stevenspointjournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"0889c929-640b-4a63-a0fe-1da9856c4e77"}]}
Creating NS1 Record: account-stage.storycityherald.com
::error::Failed to create record: account-stage.storycityherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"0aa7bbfc-ff16-44d2-82cd-9593f30c2d5d"}]}
Creating NS1 Record: account-stage.sturgisjournal.com
::error::Failed to create record: account-stage.sturgisjournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"524ee07c-f2fb-4964-a327-20154f4fedde"}]}
Creating NS1 Record: account-stage.sussexcountian.com
::error::Failed to create record: account-stage.sussexcountian.com
{"message":"record already exists","details":[{"type":"request-id","message":"cbeeae4a-1e49-4cf4-95fa-cf3a8ff712c3"}]}
Creating NS1 Record: account-stage.swtimes.com
::error::Failed to create record: account-stage.swtimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"10f62f40-c901-4fd2-b466-0e37fc9508a4"}]}
Creating NS1 Record: account-stage.tallahassee.com
::error::Failed to create record: account-stage.tallahassee.com
{"message":"record already exists","details":[{"type":"request-id","message":"e548b5e2-902b-48da-af57-851b3b29ddd0"}]}
Creating NS1 Record: account-stage.tauntongazette.com
::error::Failed to create record: account-stage.tauntongazette.com
{"message":"record already exists","details":[{"type":"request-id","message":"********-8032-45a9-9403-00ef74ddb5dc"}]}
Creating NS1 Record: account-stage.tcpalm.com
::error::Failed to create record: account-stage.tcpalm.com
{"message":"record already exists","details":[{"type":"request-id","message":"841267ad-557b-45bf-bf07-8d6c863cf43d"}]}
Creating NS1 Record: account-stage.telegram.com
::error::Failed to create record: account-stage.telegram.com
{"message":"record already exists","details":[{"type":"request-id","message":"d8a6299b-fabc-4fd4-97bc-f075de89aeff"}]}
Creating NS1 Record: account-stage.tennessean.com
::error::Failed to create record: account-stage.tennessean.com
{"message":"record already exists","details":[{"type":"request-id","message":"f2979e29-2063-4a16-b871-fd3162b5f231"}]}
Creating NS1 Record: account-stage.the-daily-record.com
::error::Failed to create record: account-stage.the-daily-record.com
{"message":"record already exists","details":[{"type":"request-id","message":"260d261a-5a86-4e5e-8b81-875492d806eb"}]}
Creating NS1 Record: account-stage.the-leader.com
::error::Failed to create record: account-stage.the-leader.com
{"message":"record already exists","details":[{"type":"request-id","message":"f6eff725-d045-4134-a5c4-39ee3557b7f8"}]}
Creating NS1 Record: account-stage.the-review.com
::error::Failed to create record: account-stage.the-review.com
{"message":"record already exists","details":[{"type":"request-id","message":"966c436d-b26d-4940-90de-d1ac37c7dc14"}]}
Creating NS1 Record: account-stage.theadvertiser.com
::error::Failed to create record: account-stage.theadvertiser.com
{"message":"record already exists","details":[{"type":"request-id","message":"bf998c4c-8a8a-42af-986a-86b537be6251"}]}
Creating NS1 Record: account-stage.thecalifornian.com
::error::Failed to create record: account-stage.thecalifornian.com
{"message":"record already exists","details":[{"type":"request-id","message":"f2b8d5cc-71eb-4bc1-8d3e-2c9024114883"}]}
Creating NS1 Record: account-stage.thedailyjournal.com
::error::Failed to create record: account-stage.thedailyjournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"3b4421de-5286-4170-bff5-90fa79277fb0"}]}
Creating NS1 Record: account-stage.thedailyreporter.com
::error::Failed to create record: account-stage.thedailyreporter.com
{"message":"record already exists","details":[{"type":"request-id","message":"c2d47fd4-b542-4cfe-a608-dce1d74da376"}]}
Creating NS1 Record: account-stage.thedestinlog.com
::error::Failed to create record: account-stage.thedestinlog.com
{"message":"record already exists","details":[{"type":"request-id","message":"1b9fafcf-b705-9709-8783-627c28d208e0"}]}
Creating NS1 Record: account-stage.thegardnernews.com
::error::Failed to create record: account-stage.thegardnernews.com
{"message":"record already exists","details":[{"type":"request-id","message":"b593517c-a28f-41bf-a9ea-63046181d165"}]}
Creating NS1 Record: account-stage.thegleaner.com
::error::Failed to create record: account-stage.thegleaner.com
{"message":"record already exists","details":[{"type":"request-id","message":"88c6aea2-8184-4ebb-9958-f30b6cf68568"}]}
Creating NS1 Record: account-stage.thehammontonnews.com
::error::Failed to create record: account-stage.thehammontonnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"********-5d66-4624-a3ad-17012b0fcf63"}]}
Creating NS1 Record: account-stage.theintell.com
::error::Failed to create record: account-stage.theintell.com
{"message":"record already exists","details":[{"type":"request-id","message":"f4d2ee11-af9f-45d8-9f61-cbaf482dde53"}]}
Creating NS1 Record: account-stage.theleafchronicle.com
::error::Failed to create record: account-stage.theleafchronicle.com
{"message":"record already exists","details":[{"type":"request-id","message":"35c2da4f-5b94-495e-877f-c3ba3ebad876"}]}
Creating NS1 Record: account-stage.theledger.com
::error::Failed to create record: account-stage.theledger.com
{"message":"record already exists","details":[{"type":"request-id","message":"c50a7a48-f465-4475-a266-eb315eac0751"}]}
Creating NS1 Record: account-stage.thenews-messenger.com
::error::Failed to create record: account-stage.thenews-messenger.com
{"message":"record already exists","details":[{"type":"request-id","message":"c3a06b94-16aa-4a50-a2e4-8ff4d0518552"}]}
Creating NS1 Record: account-stage.thenewsstar.com
::error::Failed to create record: account-stage.thenewsstar.com
{"message":"record already exists","details":[{"type":"request-id","message":"c013029d-02d1-4d42-a8c7-08d8db9d7ae2"}]}
Creating NS1 Record: account-stage.thenorthwestern.com
::error::Failed to create record: account-stage.thenorthwestern.com
{"message":"record already exists","details":[{"type":"request-id","message":"91e942c2-c077-42e4-8440-133461b8c23b"}]}
Creating NS1 Record: account-stage.theperrychief.com
::error::Failed to create record: account-stage.theperrychief.com
{"message":"record already exists","details":[{"type":"request-id","message":"8fbc7569-be7a-40fb-815e-32165fca0d4b"}]}
Creating NS1 Record: account-stage.thepublicopinion.com
::error::Failed to create record: account-stage.thepublicopinion.com
{"message":"record already exists","details":[{"type":"request-id","message":"d6b54609-f04a-4e82-8220-7f0d06c6b6e1"}]}
Creating NS1 Record: account-stage.therecordherald.com
::error::Failed to create record: account-stage.therecordherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"419fba95-06fe-4a01-8af2-0625ff6ee128"}]}
Creating NS1 Record: account-stage.thespectrum.com
::error::Failed to create record: account-stage.thespectrum.com
{"message":"record already exists","details":[{"type":"request-id","message":"0ba12238-218f-47d3-878d-87cdb3af9854"}]}
Creating NS1 Record: account-stage.thestarpress.com
::error::Failed to create record: account-stage.thestarpress.com
{"message":"record already exists","details":[{"type":"request-id","message":"f0b6856b-f788-4091-a698-8f8eab41bcd3"}]}
Creating NS1 Record: account-stage.thesuburbanite.com
::error::Failed to create record: account-stage.thesuburbanite.com
{"message":"record already exists","details":[{"type":"request-id","message":"4ded5acc-cf6d-4220-b6f9-8cfdbc8ea89a"}]}
Creating NS1 Record: account-stage.thetimesherald.com
::error::Failed to create record: account-stage.thetimesherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"ea99cbad-2fd0-400a-8238-90d7f4d457a1"}]}
Creating NS1 Record: account-stage.thetowntalk.com
::error::Failed to create record: account-stage.thetowntalk.com
{"message":"record already exists","details":[{"type":"request-id","message":"ab9658f7-92a2-43c7-a60f-b429f57e60e5"}]}
Creating NS1 Record: account-stage.thisweeknews.com
::error::Failed to create record: account-stage.thisweeknews.com
{"message":"record already exists","details":[{"type":"request-id","message":"af6b1bcb-a539-4eb5-8283-ea1d03720505"}]}
Creating NS1 Record: account-stage.tidesports.com
::error::Failed to create record: account-stage.tidesports.com
{"message":"record already exists","details":[{"type":"request-id","message":"df33b59c-a75b-4b4a-8966-1824342ca287"}]}
Creating NS1 Record: account-stage.times-gazette.com
::error::Failed to create record: account-stage.times-gazette.com
{"message":"record already exists","details":[{"type":"request-id","message":"3d4d49f7-f70f-4e8c-91ff-92c84ebbd4f5"}]}
Creating NS1 Record: account-stage.timesonline.com
::error::Failed to create record: account-stage.timesonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"2be9392a-7172-44cd-8fb2-81ee6aa9d9fd"}]}
Creating NS1 Record: account-stage.timesrecordnews.com
::error::Failed to create record: account-stage.timesrecordnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"8b0f7019-0141-4ff6-8334-e815b1318980"}]}
Creating NS1 Record: account-stage.timesreporter.com
::error::Failed to create record: account-stage.timesreporter.com
{"message":"record already exists","details":[{"type":"request-id","message":"cc772b9c-8b6b-4059-afea-582186475c66"}]}
Creating NS1 Record: account-stage.timestelegram.com
::error::Failed to create record: account-stage.timestelegram.com
{"message":"record already exists","details":[{"type":"request-id","message":"4f1a0e40-8118-49e4-8708-4523acbc1a51"}]}
Creating NS1 Record: account-stage.tmnews.com
::error::Failed to create record: account-stage.tmnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"c038903c-421b-4cb1-b9fa-fcbf9572e0fb"}]}
Creating NS1 Record: account-stage.tricountyindependent.com
::error::Failed to create record: account-stage.tricountyindependent.com
{"message":"record already exists","details":[{"type":"request-id","message":"c48a10cb-8b8d-4809-96f3-137029d19217"}]}
Creating NS1 Record: account-stage.tricountytimes.com
::error::Failed to create record: account-stage.tricountytimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"bff9d1a9-58a9-4f82-a385-4826eec1098c"}]}
Creating NS1 Record: account-stage.tucson.com
::error::Failed to create record: account-stage.tucson.com
{"message":"record already exists","details":[{"type":"request-id","message":"06a86c5e-bcb5-47a4-a5e8-f1b966dae0a6"}]}
Creating NS1 Record: account-stage.tuscaloosanews.com
::error::Failed to create record: account-stage.tuscaloosanews.com
{"message":"record already exists","details":[{"type":"request-id","message":"f4c5f334-e9b6-4474-8dfa-e7afac99db98"}]}
Creating NS1 Record: account-stage.upstateparent.com
::error::Failed to create record: account-stage.upstateparent.com
{"message":"record already exists","details":[{"type":"request-id","message":"5839a534-c961-4156-9d04-c3215ab3ed3a"}]}
Creating NS1 Record: account-stage.usatoday.com
::error::Failed to create record: account-stage.usatoday.com
{"message":"record already exists","details":[{"type":"request-id","message":"d3d7f08c-c44f-4b89-a71f-61ddd4106b06"}]}
Creating NS1 Record: account-stage.usatodaynetwork.com
Successfully created record for account-stage.usatodaynetwork.com
Creating NS1 Record: account-stage.usatodaysportsplus.com
::error::Failed to create record: account-stage.usatodaysportsplus.com
{"message":"record already exists","details":[{"type":"request-id","message":"6a6ccc03-c585-4ecf-8b6e-9ae5c50e96e7"}]}
Creating NS1 Record: account-stage.uticaod.com
::error::Failed to create record: account-stage.uticaod.com
{"message":"record already exists","details":[{"type":"request-id","message":"a47806c2-45ff-425f-a06a-c1e61f5c7037"}]}
Creating NS1 Record: account-stage.vcstar.com
::error::Failed to create record: account-stage.vcstar.com
{"message":"record already exists","details":[{"type":"request-id","message":"084c2ebf-0e1d-4952-87ee-118af0c88aa8"}]}
Creating NS1 Record: account-stage.visaliatimesdelta.com
::error::Failed to create record: account-stage.visaliatimesdelta.com
{"message":"record already exists","details":[{"type":"request-id","message":"7649206d-1b31-4b20-87ce-969e2506fadc"}]}
Creating NS1 Record: account-stage.vvdailypress.com
::error::Failed to create record: account-stage.vvdailypress.com
{"message":"record already exists","details":[{"type":"request-id","message":"27a55157-c4e7-4f5d-808d-002fc729286b"}]}
Creating NS1 Record: account-stage.waltonsun.com
::error::Failed to create record: account-stage.waltonsun.com
{"message":"record already exists","details":[{"type":"request-id","message":"da8825a7-b50d-4e38-bca8-a9c8f966ab0f"}]}
Creating NS1 Record: account-stage.washingtontimesreporter.com
::error::Failed to create record: account-stage.washingtontimesreporter.com
{"message":"record already exists","details":[{"type":"request-id","message":"c7378174-d59b-4338-aeba-3eddff6e3170"}]}
Creating NS1 Record: account-stage.wausaudailyherald.com
::error::Failed to create record: account-stage.wausaudailyherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"870c6f57-e636-44e0-aeaf-d476876af46c"}]}
Creating NS1 Record: account-stage.waynepost.com
::error::Failed to create record: account-stage.waynepost.com
{"message":"record already exists","details":[{"type":"request-id","message":"d039ded1-7d39-4da2-94f1-fdcae96a189c"}]}
Creating NS1 Record: account-stage.weeklycitizen.com
::error::Failed to create record: account-stage.weeklycitizen.com
{"message":"record already exists","details":[{"type":"request-id","message":"82f8f618-40ea-48a9-bd9d-d775f9f49443"}]}
Creating NS1 Record: account-stage.wellsvilledaily.com
::error::Failed to create record: account-stage.wellsvilledaily.com
{"message":"record already exists","details":[{"type":"request-id","message":"057db30c-2525-4d70-b068-ca4a0cac7cf5"}]}
Creating NS1 Record: account-stage.wickedlocal.com
::error::Failed to create record: account-stage.wickedlocal.com
{"message":"record already exists","details":[{"type":"request-id","message":"9c101020-0527-4593-be3f-0679316571a4"}]}
Creating NS1 Record: account-stage.wisconsinrapidstribune.com
::error::Failed to create record: account-stage.wisconsinrapidstribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"323d7105-3aeb-46e3-9f72-fdfad2440c49"}]}
Creating NS1 Record: account-stage.wisfarmer.com
::error::Failed to create record: account-stage.wisfarmer.com
{"message":"record already exists","details":[{"type":"request-id","message":"7ad813ce-cb54-4950-8b7a-516a6dbd936b"}]}
Creating NS1 Record: account-stage.woodfordtimes.com
::error::Failed to create record: account-stage.woodfordtimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"362d2098-61b3-4639-99ce-cfbd24db629d"}]}
Creating NS1 Record: account-stage.worcestermag.com
::error::Failed to create record: account-stage.worcestermag.com
{"message":"record already exists","details":[{"type":"request-id","message":"82cf611c-45c1-4587-b384-fe2be80657e1"}]}
Creating NS1 Record: account-stage.ydr.com
::error::Failed to create record: account-stage.ydr.com
{"message":"record already exists","details":[{"type":"request-id","message":"3858efd7-0b18-4e7e-b172-86d9d1e52be5"}]}
Creating NS1 Record: account-stage.yorkdispatch.com
::error::Failed to create record: account-stage.yorkdispatch.com
{"message":"record already exists","details":[{"type":"request-id","message":"ba0aae6c-a91b-4037-8ab1-6826644dd3fb"}]}
Creating NS1 Record: account-stage.zanesvilletimesrecorder.com
::error::Failed to create record: account-stage.zanesvilletimesrecorder.com
{"message":"record already exists","details":[{"type":"request-id","message":"df5b97f5-7e7a-468c-9caf-675ec7ab7208"}]}
