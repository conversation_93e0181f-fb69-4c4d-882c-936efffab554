#!/bin/bash

# Define the clusters
eastclusters=("edna-buchanan" "steve-starr" "eric-eyre")
westclusters=("bruce-russell" "raquel-rutledge" "homer-bigart")

# Loop through each cluster and run the gcloud command
for CLUSTER in "${eastclusters[@]}"; do
  echo "Getting credentials for cluster: $CLUSTER"
  gcloud container clusters get-credentials $CLUSTER --region us-east1 --project gannett-$CLUSTER
done

for CLUSTER in "${westclusters[@]}"; do
  echo "Getting credentials for cluster: $CLUSTER"
  gcloud container clusters get-credentials $CLUSTER --region us-west1 --project gannett-$CLUSTER
done


kubectl config get-contexts

# eric-eyre sre-production-us-east1 *
# barbara-laker sre-sandbox-us-east1
# bruce-russell ?
# homer-bigart sre-production-us-west1-public
# edna-buchanan sre-pre-production-us-east1 *
# raquel-rutledge sre-production-us-west1 *
# steve-starr sre-pre-production-us-east1-public