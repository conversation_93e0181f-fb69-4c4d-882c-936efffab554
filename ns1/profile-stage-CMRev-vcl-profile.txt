Creating NS1 Record: profile-stage.aberdeennews.com
::error::Failed to create record: profile-stage.aberdeennews.com
{"message":"record already exists","details":[{"type":"request-id","message":"23f339f7-965e-4649-a59e-a0c2422d6b8f"}]}
Creating NS1 Record: profile-stage.adelnews.com
::error::Failed to create record: profile-stage.adelnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"561fd502-776b-4025-8da1-e1856fd22023"}]}
Creating NS1 Record: profile-stage.aledotimesrecord.com
::error::Failed to create record: profile-stage.aledotimesrecord.com
{"message":"record already exists","details":[{"type":"request-id","message":"0a43f3e2-1a91-4d36-86e6-0a388e51f061"}]}
Creating NS1 Record: profile-stage.amarillo.com
::error::Failed to create record: profile-stage.amarillo.com
{"message":"record already exists","details":[{"type":"request-id","message":"d09152f7-349e-4587-8f50-2a5be5dddbb6"}]}
Creating NS1 Record: profile-stage.amestrib.com
::error::Failed to create record: profile-stage.amestrib.com
{"message":"record already exists","details":[{"type":"request-id","message":"470ebfa2-a27b-4d8d-9e48-7b8568eab568"}]}
Creating NS1 Record: profile-stage.app.com
Successfully created record for profile-stage.app.com
Creating NS1 Record: profile-stage.argusleader.com
Successfully created record for profile-stage.argusleader.com
Creating NS1 Record: profile-stage.augustachronicle.com
::error::Failed to create record: profile-stage.augustachronicle.com
{"message":"record already exists","details":[{"type":"request-id","message":"bc833f18-ae3c-45da-9c8c-4f450cade410"}]}
Creating NS1 Record: profile-stage.austin360.com
::error::Failed to create record: profile-stage.austin360.com
{"message":"record already exists","details":[{"type":"request-id","message":"75b38419-09d2-4a6c-8774-57940fea0ffd"}]}
Creating NS1 Record: profile-stage.azcentral.com
Successfully created record for profile-stage.azcentral.com
Creating NS1 Record: profile-stage.barnesville-enterprise.com
::error::Failed to create record: profile-stage.barnesville-enterprise.com
{"message":"record already exists","details":[{"type":"request-id","message":"0e3a263f-a00c-43ae-bea7-1fcf3b4e4be4"}]}
Creating NS1 Record: profile-stage.barnstablepatriot.com
::error::Failed to create record: profile-stage.barnstablepatriot.com
{"message":"record already exists","details":[{"type":"request-id","message":"275ba65a-1459-4721-a1f7-c30cb847599c"}]}
Creating NS1 Record: profile-stage.battlecreekenquirer.com
Successfully created record for profile-stage.battlecreekenquirer.com
Creating NS1 Record: profile-stage.baystateparent.com
::error::Failed to create record: profile-stage.baystateparent.com
{"message":"record already exists","details":[{"type":"request-id","message":"d17f397c-c80e-4c58-ba77-4370ca4e9ac4"}]}
Creating NS1 Record: profile-stage.beaconjournal.com
::error::Failed to create record: profile-stage.beaconjournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"e1e577e7-7f80-4ba7-8bd2-d2c3d537166d"}]}
Creating NS1 Record: profile-stage.blackmountainnews.com
Successfully created record for profile-stage.blackmountainnews.com
Creating NS1 Record: profile-stage.blueridgenow.com
::error::Failed to create record: profile-stage.blueridgenow.com
{"message":"record already exists","details":[{"type":"request-id","message":"c5a52072-b28e-4e01-b5ea-4929f3cdba1d"}]}
Creating NS1 Record: profile-stage.blufftontoday.com
::error::Failed to create record: profile-stage.blufftontoday.com
{"message":"record already exists","details":[{"type":"request-id","message":"ed98ae0e-3db7-4666-ba8a-498173559035"}]}
Creating NS1 Record: profile-stage.boonevilledemocrat.com
::error::Failed to create record: profile-stage.boonevilledemocrat.com
{"message":"record already exists","details":[{"type":"request-id","message":"b7b20004-73d7-4e6f-9288-c9e6e2560143"}]}
Creating NS1 Record: profile-stage.buckeyextra.com
::error::Failed to create record: profile-stage.buckeyextra.com
{"message":"record already exists","details":[{"type":"request-id","message":"247497bc-8c9c-437b-89ab-2c5064738bfb"}]}
Creating NS1 Record: profile-stage.buckscountycouriertimes.com
::error::Failed to create record: profile-stage.buckscountycouriertimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"3d980ac3-e787-4b59-a7f6-d45d0f4697fd"}]}
Creating NS1 Record: profile-stage.bucyrustelegraphforum.com
Successfully created record for profile-stage.bucyrustelegraphforum.com
Creating NS1 Record: profile-stage.burlingtoncountytimes.com
::error::Failed to create record: profile-stage.burlingtoncountytimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"e2753a43-16f3-45a1-b92f-ed2c0e285d3b"}]}
Creating NS1 Record: profile-stage.burlingtonfreepress.com
Successfully created record for profile-stage.burlingtonfreepress.com
Creating NS1 Record: profile-stage.caller.com
Successfully created record for profile-stage.caller.com
Creating NS1 Record: profile-stage.cantondailyledger.com
::error::Failed to create record: profile-stage.cantondailyledger.com
{"message":"record already exists","details":[{"type":"request-id","message":"f9a58be0-2bc8-4264-8261-15432a658b12"}]}
Creating NS1 Record: profile-stage.cantonrep.com
::error::Failed to create record: profile-stage.cantonrep.com
{"message":"record already exists","details":[{"type":"request-id","message":"4a5c4ee6-4b21-4de7-bd2a-0e2aea9a9990"}]}
Creating NS1 Record: profile-stage.capecodtimes.com
::error::Failed to create record: profile-stage.capecodtimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"aef86cc9-51fe-43b0-bda8-2131589cffd2"}]}
Creating NS1 Record: profile-stage.charlestonexpress.com
::error::Failed to create record: profile-stage.charlestonexpress.com
{"message":"record already exists","details":[{"type":"request-id","message":"612e7531-6960-4bab-b58c-ce1e9e4da839"}]}
Creating NS1 Record: profile-stage.cheboygannews.com
::error::Failed to create record: profile-stage.cheboygannews.com
{"message":"record already exists","details":[{"type":"request-id","message":"7fcde00f-36dc-4dd4-83d4-666116a3585a"}]}
Creating NS1 Record: profile-stage.chieftain.com
::error::Failed to create record: profile-stage.chieftain.com
{"message":"record already exists","details":[{"type":"request-id","message":"b41bf1b6-1daa-437d-bc6d-fad68183f943"}]}
Creating NS1 Record: profile-stage.chillicothegazette.com
Successfully created record for profile-stage.chillicothegazette.com
Creating NS1 Record: profile-stage.chillicothetimesbulletin.com
::error::Failed to create record: profile-stage.chillicothetimesbulletin.com
{"message":"record already exists","details":[{"type":"request-id","message":"a8904646-b68b-410c-ba9d-42cf602b6a98"}]}
Creating NS1 Record: profile-stage.cincinnati.com
Successfully created record for profile-stage.cincinnati.com
Creating NS1 Record: profile-stage.citizen-times.com
Successfully created record for profile-stage.citizen-times.com
Creating NS1 Record: profile-stage.cjonline.com
::error::Failed to create record: profile-stage.cjonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"07360446-0f7c-4f8d-9739-981194cc3dc4"}]}
Creating NS1 Record: profile-stage.clarionledger.com
Successfully created record for profile-stage.clarionledger.com
Creating NS1 Record: profile-stage.coloradoan.com
Successfully created record for profile-stage.coloradoan.com
Creating NS1 Record: profile-stage.columbiadailyherald.com
::error::Failed to create record: profile-stage.columbiadailyherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"e7d9d194-50ef-4648-9f4c-7331978de789"}]}
Creating NS1 Record: profile-stage.columbiatribune.com
::error::Failed to create record: profile-stage.columbiatribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"d082e54f-5535-4032-a8f0-d6bd937ec253"}]}
Creating NS1 Record: profile-stage.columbusalive.com
::error::Failed to create record: profile-stage.columbusalive.com
{"message":"record already exists","details":[{"type":"request-id","message":"1b946d22-a83b-49f6-87a0-3ab6add0693f"}]}
Creating NS1 Record: profile-stage.columbusceo.com
::error::Failed to create record: profile-stage.columbusceo.com
{"message":"record already exists","details":[{"type":"request-id","message":"1fcfcef5-8729-4846-b746-6e49dec359f6"}]}
Creating NS1 Record: profile-stage.columbusmonthly.com
::error::Failed to create record: profile-stage.columbusmonthly.com
{"message":"record already exists","details":[{"type":"request-id","message":"2a29d3bc-c7ee-413e-8da1-f852dbdf44cf"}]}
Creating NS1 Record: profile-stage.columbusparent.com
::error::Failed to create record: profile-stage.columbusparent.com
{"message":"record already exists","details":[{"type":"request-id","message":"9dc9f62c-eb98-4a37-a9d6-e2942daa9712"}]}
Creating NS1 Record: profile-stage.commercialappeal.com
Successfully created record for profile-stage.commercialappeal.com
Creating NS1 Record: profile-stage.coshoctontribune.com
Successfully created record for profile-stage.coshoctontribune.com
Creating NS1 Record: profile-stage.courier-journal.com
Successfully created record for profile-stage.courier-journal.com
Creating NS1 Record: profile-stage.courierpostonline.com
Successfully created record for profile-stage.courierpostonline.com
Creating NS1 Record: profile-stage.courierpress.com
Successfully created record for profile-stage.courierpress.com
Creating NS1 Record: profile-stage.daily-jeff.com
::error::Failed to create record: profile-stage.daily-jeff.com
{"message":"record already exists","details":[{"type":"request-id","message":"2f004a08-0e8e-4843-a9fc-85eb62fb6341"}]}
Creating NS1 Record: profile-stage.dailyamerican.com
::error::Failed to create record: profile-stage.dailyamerican.com
{"message":"record already exists","details":[{"type":"request-id","message":"043fb1f1-ef61-4e56-81ba-12797a7119d7"}]}
Creating NS1 Record: profile-stage.dailycomet.com
::error::Failed to create record: profile-stage.dailycomet.com
{"message":"record already exists","details":[{"type":"request-id","message":"33b522e1-c6a4-4b42-b738-1b4c50dce471"}]}
Creating NS1 Record: profile-stage.dailycommercial.com
::error::Failed to create record: profile-stage.dailycommercial.com
{"message":"record already exists","details":[{"type":"request-id","message":"b80e03bf-c628-4f05-aaf8-33faf1b295cf"}]}
Creating NS1 Record: profile-stage.dailyrecord.com
Successfully created record for profile-stage.dailyrecord.com
Creating NS1 Record: profile-stage.dailyworld.com
Successfully created record for profile-stage.dailyworld.com
Creating NS1 Record: profile-stage.dansvilleonline.com
::error::Failed to create record: profile-stage.dansvilleonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"19163d95-782a-4395-8134-9683c315a727"}]}
Creating NS1 Record: profile-stage.delawareonline.com
Successfully created record for profile-stage.delawareonline.com
Creating NS1 Record: profile-stage.delmarvanow.com
Successfully created record for profile-stage.delmarvanow.com
Creating NS1 Record: profile-stage.democratandchronicle.com
::error::Failed to create record: profile-stage.democratandchronicle.com
{"message":"record already exists","details":[{"type":"request-id","message":"7f1486b5-3460-4367-8d51-7c93fa41dc25"}]}
Creating NS1 Record: profile-stage.desertsun.com
Successfully created record for profile-stage.desertsun.com
Creating NS1 Record: profile-stage.desmoinesregister.com
Successfully created record for profile-stage.desmoinesregister.com
Creating NS1 Record: profile-stage.detroitnews.com
Successfully created record for profile-stage.detroitnews.com
Creating NS1 Record: profile-stage.dispatch.com
::error::Failed to create record: profile-stage.dispatch.com
{"message":"record already exists","details":[{"type":"request-id","message":"5faf0459-efb2-448d-80df-8f34b376127a"}]}
Creating NS1 Record: profile-stage.dmjuice.com
Successfully created record for profile-stage.dmjuice.com
Creating NS1 Record: profile-stage.dnj.com
Successfully created record for profile-stage.dnj.com
Creating NS1 Record: profile-stage.donaldsonvillechief.com
::error::Failed to create record: profile-stage.donaldsonvillechief.com
{"message":"record already exists","details":[{"type":"request-id","message":"20254c0d-d29e-462a-885a-4345f27d3af2"}]}
Creating NS1 Record: profile-stage.doverpost.com
::error::Failed to create record: profile-stage.doverpost.com
{"message":"record already exists","details":[{"type":"request-id","message":"3df04a5a-40da-49c6-b777-d4b2d7afc44b"}]}
Creating NS1 Record: profile-stage.eastpeoriatimescourier.com
::error::Failed to create record: profile-stage.eastpeoriatimescourier.com
{"message":"record already exists","details":[{"type":"request-id","message":"663f95e4-1511-49d6-9414-d0fe42779135"}]}
Creating NS1 Record: profile-stage.echo-pilot.com
::error::Failed to create record: profile-stage.echo-pilot.com
{"message":"record already exists","details":[{"type":"request-id","message":"d0c055e4-fb2b-43fd-a72a-46563a61f1d0"}]}
Creating NS1 Record: profile-stage.ellwoodcityledger.com
::error::Failed to create record: profile-stage.ellwoodcityledger.com
{"message":"record already exists","details":[{"type":"request-id","message":"56e30e80-fd34-4f06-a2dd-bd746fa2b461"}]}
Creating NS1 Record: profile-stage.elpasotimes.com
Successfully created record for profile-stage.elpasotimes.com
Creating NS1 Record: profile-stage.elpasoymas.com
Successfully created record for profile-stage.elpasoymas.com
Creating NS1 Record: profile-stage.elsoldesalinas.com
Successfully created record for profile-stage.elsoldesalinas.com
Creating NS1 Record: profile-stage.enterprisenews.com
::error::Failed to create record: profile-stage.enterprisenews.com
{"message":"record already exists","details":[{"type":"request-id","message":"dd4c7deb-6958-4d75-b6ca-1b12b54672a1"}]}
Creating NS1 Record: profile-stage.eveningsun.com
Successfully created record for profile-stage.eveningsun.com
Creating NS1 Record: profile-stage.eveningtribune.com
::error::Failed to create record: profile-stage.eveningtribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"81514d7e-dc0d-4fd8-a021-f4650cb29437"}]}
Creating NS1 Record: profile-stage.examiner-enterprise.com
::error::Failed to create record: profile-stage.examiner-enterprise.com
{"message":"record already exists","details":[{"type":"request-id","message":"5045d72d-d22a-43ca-bbe1-35e92010ee4b"}]}
Creating NS1 Record: profile-stage.farmersadvance.com
Successfully created record for profile-stage.farmersadvance.com
Creating NS1 Record: profile-stage.farmforum.net
::error::Failed to create record: profile-stage.farmforum.net
{"message":"record already exists","details":[{"type":"request-id","message":"10da1e89-99fb-444d-be4a-5f1a8646d6d1"}]}
Creating NS1 Record: profile-stage.fayobserver.com
::error::Failed to create record: profile-stage.fayobserver.com
{"message":"record already exists","details":[{"type":"request-id","message":"ec1e5a97-1d44-476e-a17f-37d98aea62fa"}]}
Creating NS1 Record: profile-stage.fdlreporter.com
Successfully created record for profile-stage.fdlreporter.com
Creating NS1 Record: profile-stage.flipsidepa.com
Successfully created record for profile-stage.flipsidepa.com
Creating NS1 Record: profile-stage.floridatoday.com
Successfully created record for profile-stage.floridatoday.com
Creating NS1 Record: profile-stage.fosters.com
::error::Failed to create record: profile-stage.fosters.com
{"message":"record already exists","details":[{"type":"request-id","message":"61e2269b-f37a-46d1-9636-d8d46a90c21b"}]}
Creating NS1 Record: profile-stage.freep.com
Successfully created record for profile-stage.freep.com
Creating NS1 Record: profile-stage.fsunews.com
Successfully created record for profile-stage.fsunews.com
Creating NS1 Record: profile-stage.gadsdentimes.com
::error::Failed to create record: profile-stage.gadsdentimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"5efd0518-8c60-4f86-a754-dbcbc5e6e67f"}]}
Creating NS1 Record: profile-stage.gainesville.com
::error::Failed to create record: profile-stage.gainesville.com
{"message":"record already exists","details":[{"type":"request-id","message":"36d76bd3-99fc-4a66-9a40-2362629fca19"}]}
Creating NS1 Record: profile-stage.galesburg.com
::error::Failed to create record: profile-stage.galesburg.com
{"message":"record already exists","details":[{"type":"request-id","message":"31679717-f50e-4494-b18e-089fce3af3ba"}]}
Creating NS1 Record: profile-stage.gametimepa.com
Successfully created record for profile-stage.gametimepa.com
Creating NS1 Record: profile-stage.gannett.com
Successfully created record for profile-stage.gannett.com
Creating NS1 Record: profile-stage.gastongazette.com
::error::Failed to create record: profile-stage.gastongazette.com
{"message":"record already exists","details":[{"type":"request-id","message":"a2994310-a877-4989-9e30-eb64f7d9bd9a"}]}
Creating NS1 Record: profile-stage.gatorsports.com
::error::Failed to create record: profile-stage.gatorsports.com
{"message":"record already exists","details":[{"type":"request-id","message":"f5e98f37-ee7c-4ba5-aa85-87725df961e5"}]}
Creating NS1 Record: profile-stage.geneseorepublic.com
::error::Failed to create record: profile-stage.geneseorepublic.com
{"message":"record already exists","details":[{"type":"request-id","message":"cb8de593-3c67-4f6e-b9fc-1f16a1250c49"}]}
Creating NS1 Record: profile-stage.goerie.com
::error::Failed to create record: profile-stage.goerie.com
{"message":"record already exists","details":[{"type":"request-id","message":"522d4256-70c3-4362-90ea-a159704c214c"}]}
Creating NS1 Record: profile-stage.gosanangelo.com
Successfully created record for profile-stage.gosanangelo.com
Creating NS1 Record: profile-stage.goupstate.com
::error::Failed to create record: profile-stage.goupstate.com
{"message":"record already exists","details":[{"type":"request-id","message":"80628b67-4a84-49bf-b60c-33e81dd46b52"}]}
Creating NS1 Record: profile-stage.greatfallstribune.com
Successfully created record for profile-stage.greatfallstribune.com
Creating NS1 Record: profile-stage.greenbaypressgazette.com
Successfully created record for profile-stage.greenbaypressgazette.com
Creating NS1 Record: profile-stage.greenvilleonline.com
Successfully created record for profile-stage.greenvilleonline.com
Creating NS1 Record: profile-stage.hattiesburgamerican.com
Successfully created record for profile-stage.hattiesburgamerican.com
Creating NS1 Record: profile-stage.hawkcentral.com
Successfully created record for profile-stage.hawkcentral.com
Creating NS1 Record: profile-stage.heraldmailmedia.com
::error::Failed to create record: profile-stage.heraldmailmedia.com
{"message":"record already exists","details":[{"type":"request-id","message":"a9eb7daf-30be-48c9-9adc-cd209842040c"}]}
Creating NS1 Record: profile-stage.heraldnews.com
::error::Failed to create record: profile-stage.heraldnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"b569b59c-98c0-4547-9325-d1dd67005770"}]}
Creating NS1 Record: profile-stage.heraldtimesonline.com
::error::Failed to create record: profile-stage.heraldtimesonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"664ce05f-0fcb-4fe5-a831-58e0c61146de"}]}
Creating NS1 Record: profile-stage.heraldtribune.com
::error::Failed to create record: profile-stage.heraldtribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"9052d338-396e-40a9-bbb0-0436967d86ed"}]}
Creating NS1 Record: profile-stage.hillsdale.net
::error::Failed to create record: profile-stage.hillsdale.net
{"message":"record already exists","details":[{"type":"request-id","message":"a32322a8-5961-4057-925e-c6f01ad289c1"}]}
Creating NS1 Record: profile-stage.hockessincommunitynews.com
::error::Failed to create record: profile-stage.hockessincommunitynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"2a5daba2-7612-4261-b651-481636d76780"}]}
Creating NS1 Record: profile-stage.hollandsentinel.com
::error::Failed to create record: profile-stage.hollandsentinel.com
{"message":"record already exists","details":[{"type":"request-id","message":"dc498bc6-ba76-41eb-b267-b303d380b3c6"}]}
Creating NS1 Record: profile-stage.hometownlife.com
Successfully created record for profile-stage.hometownlife.com
Creating NS1 Record: profile-stage.hookem.com
::error::Failed to create record: profile-stage.hookem.com
{"message":"record already exists","details":[{"type":"request-id","message":"92481832-922a-4ba3-8097-60fd310d1b15"}]}
Creating NS1 Record: profile-stage.hoopshype.com
Successfully created record for profile-stage.hoopshype.com
Creating NS1 Record: profile-stage.houmatoday.com
::error::Failed to create record: profile-stage.houmatoday.com
{"message":"record already exists","details":[{"type":"request-id","message":"46d44622-96c9-440c-b120-f438b1f248d9"}]}
Creating NS1 Record: profile-stage.htrnews.com
Successfully created record for profile-stage.htrnews.com
Creating NS1 Record: profile-stage.hutchnews.com
::error::Failed to create record: profile-stage.hutchnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"a76aa64f-e698-4df4-86db-7bdcf48dda8d"}]}
Creating NS1 Record: profile-stage.indeonline.com
::error::Failed to create record: profile-stage.indeonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"bb06a9cc-ee86-41c8-a049-854997bb4f90"}]}
Creating NS1 Record: profile-stage.independentmail.com
Successfully created record for profile-stage.independentmail.com
Creating NS1 Record: profile-stage.indystar.com
::error::Failed to create record: profile-stage.indystar.com
{"message":"record already exists","details":[{"type":"request-id","message":"c0236be8-e419-4c51-8eb8-2ead32d7a0bd"}]}
Creating NS1 Record: profile-stage.inyork.com
Successfully created record for profile-stage.inyork.com
Creating NS1 Record: profile-stage.ithacajournal.com
Successfully created record for profile-stage.ithacajournal.com
Creating NS1 Record: profile-stage.jacksonsun.com
Successfully created record for profile-stage.jacksonsun.com
Creating NS1 Record: profile-stage.jacksonville.com
::error::Failed to create record: profile-stage.jacksonville.com
{"message":"record already exists","details":[{"type":"request-id","message":"fdb590a7-74b6-4285-8ae2-a90f27a5abf0"}]}
Creating NS1 Record: profile-stage.jconline.com
::error::Failed to create record: profile-stage.jconline.com
{"message":"record already exists","details":[{"type":"request-id","message":"a7ff7e05-5800-46d2-8171-cd8dabb80311"}]}
Creating NS1 Record: profile-stage.jdnews.com
::error::Failed to create record: profile-stage.jdnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"9882752f-7c2c-4a86-9b60-6a83d6428b80"}]}
Creating NS1 Record: profile-stage.journalstandard.com
::error::Failed to create record: profile-stage.journalstandard.com
{"message":"record already exists","details":[{"type":"request-id","message":"9e230857-1d03-4582-bbb9-7d4c73ff8644"}]}
Creating NS1 Record: profile-stage.jsonline.com
Successfully created record for profile-stage.jsonline.com
Creating NS1 Record: profile-stage.kitsapsun.com
Successfully created record for profile-stage.kitsapsun.com
Creating NS1 Record: profile-stage.knoxnews.com
Successfully created record for profile-stage.knoxnews.com
Creating NS1 Record: profile-stage.lancastereaglegazette.com
Successfully created record for profile-stage.lancastereaglegazette.com
Creating NS1 Record: profile-stage.lansingstatejournal.com
Successfully created record for profile-stage.lansingstatejournal.com
Creating NS1 Record: profile-stage.lavozarizona.com
Successfully created record for profile-stage.lavozarizona.com
Creating NS1 Record: profile-stage.lcsun-news.com
Successfully created record for profile-stage.lcsun-news.com
Creating NS1 Record: profile-stage.ldnews.com
Successfully created record for profile-stage.ldnews.com
Creating NS1 Record: profile-stage.lenconnect.com
::error::Failed to create record: profile-stage.lenconnect.com
{"message":"record already exists","details":[{"type":"request-id","message":"79248098-a6aa-451e-9549-2fef17b8b35f"}]}
Creating NS1 Record: profile-stage.lincolncourier.com
::error::Failed to create record: profile-stage.lincolncourier.com
{"message":"record already exists","details":[{"type":"request-id","message":"a0ab32bc-f085-42e7-9ffc-66cee50a8170"}]}
Creating NS1 Record: profile-stage.linkbostonhomes.com
::error::Failed to create record: profile-stage.linkbostonhomes.com
{"message":"record already exists","details":[{"type":"request-id","message":"5de86d14-98e8-403f-a081-a2e6dd926df9"}]}
Creating NS1 Record: profile-stage.livingstondaily.com
Successfully created record for profile-stage.livingstondaily.com
Creating NS1 Record: profile-stage.lohud.com
Successfully created record for profile-stage.lohud.com
Creating NS1 Record: profile-stage.lubbockonline.com
::error::Failed to create record: profile-stage.lubbockonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"6bed1739-ebda-991c-b4b6-1991d15fd756"}]}
Creating NS1 Record: profile-stage.mansfieldnewsjournal.com
Successfully created record for profile-stage.mansfieldnewsjournal.com
Creating NS1 Record: profile-stage.marconews.com
Successfully created record for profile-stage.marconews.com
Creating NS1 Record: profile-stage.marionstar.com
Successfully created record for profile-stage.marionstar.com
Creating NS1 Record: profile-stage.marshfieldnewsherald.com
Successfully created record for profile-stage.marshfieldnewsherald.com
Creating NS1 Record: profile-stage.mcdonoughvoice.com
::error::Failed to create record: profile-stage.mcdonoughvoice.com
{"message":"record already exists","details":[{"type":"request-id","message":"0464e01d-20bb-4765-b78c-e869b32e2a27"}]}
Creating NS1 Record: profile-stage.metrowestdailynews.com
::error::Failed to create record: profile-stage.metrowestdailynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"515d4a35-76e7-4701-90e4-064af7addfdf"}]}
Creating NS1 Record: profile-stage.middletowntranscript.com
::error::Failed to create record: profile-stage.middletowntranscript.com
{"message":"record already exists","details":[{"type":"request-id","message":"412393fe-932a-4463-a4d9-b8d210ad4263"}]}
Creating NS1 Record: profile-stage.milfordbeacon.com
::error::Failed to create record: profile-stage.milfordbeacon.com
{"message":"record already exists","details":[{"type":"request-id","message":"b2111b14-13a8-4aac-b21c-d75b288f9dca"}]}
Creating NS1 Record: profile-stage.milforddailynews.com
::error::Failed to create record: profile-stage.milforddailynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"246a7514-219c-4b53-b055-5061470f2e2a"}]}
Creating NS1 Record: profile-stage.monroecopost.com
::error::Failed to create record: profile-stage.monroecopost.com
{"message":"record already exists","details":[{"type":"request-id","message":"6b5b5f34-7612-45a1-923c-3c230631aa2f"}]}
Creating NS1 Record: profile-stage.monroenews.com
::error::Failed to create record: profile-stage.monroenews.com
{"message":"record already exists","details":[{"type":"request-id","message":"3421db65-1ac7-4986-b68e-411002887e59"}]}
Creating NS1 Record: profile-stage.montgomeryadvertiser.com
Successfully created record for profile-stage.montgomeryadvertiser.com
Creating NS1 Record: profile-stage.mortontimesnews.com
::error::Failed to create record: profile-stage.mortontimesnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"3a339694-1dd7-494b-ae10-82fa51a806a8"}]}
Creating NS1 Record: profile-stage.mpnnow.com
::error::Failed to create record: profile-stage.mpnnow.com
{"message":"record already exists","details":[{"type":"request-id","message":"89079317-288d-4830-b35b-ea2ab8788003"}]}
Creating NS1 Record: profile-stage.mtshastanews.com
::error::Failed to create record: profile-stage.mtshastanews.com
{"message":"record already exists","details":[{"type":"request-id","message":"e2a9bd4c-2ac3-4185-b9cc-72096c8a91e1"}]}
Creating NS1 Record: profile-stage.mycentraljersey.com
Successfully created record for profile-stage.mycentraljersey.com
Creating NS1 Record: profile-stage.mytownneo.com
::error::Failed to create record: profile-stage.mytownneo.com
{"message":"record already exists","details":[{"type":"request-id","message":"52625bf2-7e0d-46d4-af39-280e7293f57a"}]}
Creating NS1 Record: profile-stage.naplesnews.com
Successfully created record for profile-stage.naplesnews.com
Creating NS1 Record: profile-stage.ndinsider.com
::error::Failed to create record: profile-stage.ndinsider.com
{"message":"record already exists","details":[{"type":"request-id","message":"d04e9ef0-473e-4c22-ab1c-7300406bf8e7"}]}
Creating NS1 Record: profile-stage.nevadaiowajournal.com
::error::Failed to create record: profile-stage.nevadaiowajournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"9514b22f-cb8c-40f4-9b12-55b27adbb86a"}]}
Creating NS1 Record: profile-stage.newarkadvocate.com
Successfully created record for profile-stage.newarkadvocate.com
Creating NS1 Record: profile-stage.newportri.com
::error::Failed to create record: profile-stage.newportri.com
{"message":"record already exists","details":[{"type":"request-id","message":"b1e7af15-179d-45a6-ab4d-1bd1e67b7855"}]}
Creating NS1 Record: profile-stage.news-journalonline.com
::error::Failed to create record: profile-stage.news-journalonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"3d32fcf0-fead-4aa6-a4b6-786d4be1ce31"}]}
Creating NS1 Record: profile-stage.news-leader.com
Successfully created record for profile-stage.news-leader.com
Creating NS1 Record: profile-stage.news-press.com
Successfully created record for profile-stage.news-press.com
Creating NS1 Record: profile-stage.newschief.com
::error::Failed to create record: profile-stage.newschief.com
{"message":"record already exists","details":[{"type":"request-id","message":"55e96249-5fc9-4cd3-a7a0-be8db7cddbf3"}]}
Creating NS1 Record: profile-stage.newsherald.com
::error::Failed to create record: profile-stage.newsherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"439e8690-6178-4297-92b4-7c563432cc83"}]}
Creating NS1 Record: profile-stage.newsleader.com
Successfully created record for profile-stage.newsleader.com
Creating NS1 Record: profile-stage.newsrepublican.com
::error::Failed to create record: profile-stage.newsrepublican.com
{"message":"record already exists","details":[{"type":"request-id","message":"8fb1f49d-ad5b-46bf-9e0d-579940761d3f"}]}
Creating NS1 Record: profile-stage.njherald.com
::error::Failed to create record: profile-stage.njherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"9901c1b7-3515-4029-85d9-5432d7ac0f0b"}]}
Creating NS1 Record: profile-stage.northjersey.com
Successfully created record for profile-stage.northjersey.com
Creating NS1 Record: profile-stage.norwichbulletin.com
::error::Failed to create record: profile-stage.norwichbulletin.com
{"message":"record already exists","details":[{"type":"request-id","message":"ab2c6d5c-364f-4559-8394-833236ab0655"}]}
Creating NS1 Record: profile-stage.nwfdailynews.com
::error::Failed to create record: profile-stage.nwfdailynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"22f8fecf-a137-4eac-926c-4fcc8168488b"}]}
Creating NS1 Record: profile-stage.oakridger.com
::error::Failed to create record: profile-stage.oakridger.com
{"message":"record already exists","details":[{"type":"request-id","message":"d179b277-8f26-45aa-a097-113596061f40"}]}
Creating NS1 Record: profile-stage.ocala.com
::error::Failed to create record: profile-stage.ocala.com
{"message":"record already exists","details":[{"type":"request-id","message":"07c9cd90-3aa6-4757-a3d6-7da11ce6889b"}]}
Creating NS1 Record: profile-stage.oklahoman.com
::error::Failed to create record: profile-stage.oklahoman.com
{"message":"record already exists","details":[{"type":"request-id","message":"7992f202-7b0f-416f-b172-d1f61f972dd6"}]}
Creating NS1 Record: profile-stage.onlineathens.com
::error::Failed to create record: profile-stage.onlineathens.com
{"message":"record already exists","details":[{"type":"request-id","message":"067f2604-2217-4408-a4a8-224e81886691"}]}
Creating NS1 Record: profile-stage.packersnews.com
Successfully created record for profile-stage.packersnews.com
Creating NS1 Record: profile-stage.pal-item.com
Successfully created record for profile-stage.pal-item.com
Creating NS1 Record: profile-stage.palmbeachdailynews.com
::error::Failed to create record: profile-stage.palmbeachdailynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"a68d316e-b2d7-4f1f-ba45-74afaf8846e0"}]}
Creating NS1 Record: profile-stage.palmbeachpost.com
::error::Failed to create record: profile-stage.palmbeachpost.com
{"message":"record already exists","details":[{"type":"request-id","message":"10cc6310-e0c2-4c93-ab2c-6c9c75e22079"}]}
Creating NS1 Record: profile-stage.paris-express.com
::error::Failed to create record: profile-stage.paris-express.com
{"message":"record already exists","details":[{"type":"request-id","message":"af388011-c5ca-49e4-b0f9-a9dd2f1e10d4"}]}
Creating NS1 Record: profile-stage.patriotledger.com
::error::Failed to create record: profile-stage.patriotledger.com
{"message":"record already exists","details":[{"type":"request-id","message":"aee3d3a4-1dea-46d0-b11b-b15ceb13668b"}]}
Creating NS1 Record: profile-stage.pawhuskajournalcapital.com
::error::Failed to create record: profile-stage.pawhuskajournalcapital.com
{"message":"record already exists","details":[{"type":"request-id","message":"271b6ce5-4a6c-4993-8673-ac40b9a7da59"}]}
Creating NS1 Record: profile-stage.pekintimes.com
::error::Failed to create record: profile-stage.pekintimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"7ebf40a4-25ab-4582-9500-eb925a09ea53"}]}
Creating NS1 Record: profile-stage.petoskeynews.com
::error::Failed to create record: profile-stage.petoskeynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"14dab907-25f2-478b-b848-8ea52faee3f9"}]}
Creating NS1 Record: profile-stage.phillyburbs.com
Successfully created record for profile-stage.phillyburbs.com
Creating NS1 Record: profile-stage.pjstar.com
::error::Failed to create record: profile-stage.pjstar.com
{"message":"record already exists","details":[{"type":"request-id","message":"c5e18ef2-2a49-4a63-b9b4-37bba5dc4ef0"}]}
Creating NS1 Record: profile-stage.pnj.com
Successfully created record for profile-stage.pnj.com
Creating NS1 Record: profile-stage.poconorecord.com
::error::Failed to create record: profile-stage.poconorecord.com
{"message":"record already exists","details":[{"type":"request-id","message":"2641acfc-cca6-4023-b355-482c001f5098"}]}
Creating NS1 Record: profile-stage.pontiacdailyleader.com
::error::Failed to create record: profile-stage.pontiacdailyleader.com
{"message":"record already exists","details":[{"type":"request-id","message":"9b47234e-45b3-4aea-92ad-23c933099d85"}]}
Creating NS1 Record: profile-stage.portclintonnewsherald.com
Successfully created record for profile-stage.portclintonnewsherald.com
Creating NS1 Record: profile-stage.postcrescent.com
Successfully created record for profile-stage.postcrescent.com
Creating NS1 Record: profile-stage.postsouth.com
::error::Failed to create record: profile-stage.postsouth.com
{"message":"record already exists","details":[{"type":"request-id","message":"d2d12ff0-c59b-4f39-8cac-74e98f75d4d2"}]}
Creating NS1 Record: profile-stage.poughkeepsiejournal.com
Successfully created record for profile-stage.poughkeepsiejournal.com
Creating NS1 Record: profile-stage.press-citizen.com
Successfully created record for profile-stage.press-citizen.com
Creating NS1 Record: profile-stage.pressargus.com
::error::Failed to create record: profile-stage.pressargus.com
{"message":"record already exists","details":[{"type":"request-id","message":"a7561789-5cd4-440a-b529-3c0404825f67"}]}
Creating NS1 Record: profile-stage.pressconnects.com
Successfully created record for profile-stage.pressconnects.com
Creating NS1 Record: profile-stage.progress-index.com
::error::Failed to create record: profile-stage.progress-index.com
{"message":"record already exists","details":[{"type":"request-id","message":"be1f7cfa-3e83-4bf5-aeb0-0628f7285ca7"}]}
Creating NS1 Record: profile-stage.providencejournal.com
::error::Failed to create record: profile-stage.providencejournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"10d3d619-d7df-44d2-9adc-d4a10c717505"}]}
Creating NS1 Record: profile-stage.publicopiniononline.com
Successfully created record for profile-stage.publicopiniononline.com
Creating NS1 Record: profile-stage.record-courier.com
::error::Failed to create record: profile-stage.record-courier.com
{"message":"record already exists","details":[{"type":"request-id","message":"898db174-0610-4a62-8a3b-a967ff7ad617"}]}
Creating NS1 Record: profile-stage.recordnet.com
::error::Failed to create record: profile-stage.recordnet.com
{"message":"record already exists","details":[{"type":"request-id","message":"6b4b76a8-d0f1-4e3e-b82a-569f8da7d146"}]}
Creating NS1 Record: profile-stage.recordonline.com
::error::Failed to create record: profile-stage.recordonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"b73fe15c-dacc-4d92-b002-f59f6e0bed5a"}]}
Creating NS1 Record: profile-stage.recordstar.com
::error::Failed to create record: profile-stage.recordstar.com
{"message":"record already exists","details":[{"type":"request-id","message":"45d153f5-1e07-4de7-94d4-e6ac6e3ccd3c"}]}
Creating NS1 Record: profile-stage.redding.com
Successfully created record for profile-stage.redding.com
Creating NS1 Record: profile-stage.registerguard.com
::error::Failed to create record: profile-stage.registerguard.com
{"message":"record already exists","details":[{"type":"request-id","message":"0a9cb85b-d6b2-4109-9dc6-c6bc5a01e971"}]}
Creating NS1 Record: profile-stage.reporter-times.com
::error::Failed to create record: profile-stage.reporter-times.com
{"message":"record already exists","details":[{"type":"request-id","message":"0e4a949d-f0ae-4a1d-b6df-d8af4793ad96"}]}
Creating NS1 Record: profile-stage.reporternews.com
Successfully created record for profile-stage.reporternews.com
Creating NS1 Record: profile-stage.reviewatlas.com
::error::Failed to create record: profile-stage.reviewatlas.com
{"message":"record already exists","details":[{"type":"request-id","message":"35237b30-05c2-4ef7-ae4c-1f822c4137e9"}]}
Creating NS1 Record: profile-stage.rgj.com
Successfully created record for profile-stage.rgj.com
Creating NS1 Record: profile-stage.rrstar.com
::error::Failed to create record: profile-stage.rrstar.com
{"message":"record already exists","details":[{"type":"request-id","message":"70ce5491-19cb-49e9-bc9b-d3b3168b0f44"}]}
Creating NS1 Record: profile-stage.ruidosonews.com
Successfully created record for profile-stage.ruidosonews.com
Creating NS1 Record: profile-stage.salina.com
::error::Failed to create record: profile-stage.salina.com
{"message":"record already exists","details":[{"type":"request-id","message":"404260cb-123b-4aba-955b-8159c70b76b0"}]}
Creating NS1 Record: profile-stage.savannahnow.com
::error::Failed to create record: profile-stage.savannahnow.com
{"message":"record already exists","details":[{"type":"request-id","message":"ae67eccc-efd5-419e-a99f-01d5911023c2"}]}
Creating NS1 Record: profile-stage.scsuntimes.com
::error::Failed to create record: profile-stage.scsuntimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"dcd350bd-6d21-4bac-9a31-10ec374d02c4"}]}
Creating NS1 Record: profile-stage.sctimes.com
Successfully created record for profile-stage.sctimes.com
Creating NS1 Record: profile-stage.seacoastonline.com
::error::Failed to create record: profile-stage.seacoastonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"fbec4a5f-ca87-4cc5-b64a-087942fe7d93"}]}
Creating NS1 Record: profile-stage.sentinel-standard.com
::error::Failed to create record: profile-stage.sentinel-standard.com
{"message":"record already exists","details":[{"type":"request-id","message":"19f3236d-af9a-46ce-95e7-de1f252355d4"}]}
Creating NS1 Record: profile-stage.sheboyganpress.com
Successfully created record for profile-stage.sheboyganpress.com
Creating NS1 Record: profile-stage.shelbystar.com
::error::Failed to create record: profile-stage.shelbystar.com
{"message":"record already exists","details":[{"type":"request-id","message":"ab7342bb-0f6d-4e4c-9bb3-2850dd52c913"}]}
Creating NS1 Record: profile-stage.shreveporttimes.com
Successfully created record for profile-stage.shreveporttimes.com
Creating NS1 Record: profile-stage.siskiyoudaily.com
::error::Failed to create record: profile-stage.siskiyoudaily.com
{"message":"record already exists","details":[{"type":"request-id","message":"f7c69ac5-609d-4612-9fa4-514d76c80a7d"}]}
Creating NS1 Record: profile-stage.sj-r.com
::error::Failed to create record: profile-stage.sj-r.com
{"message":"record already exists","details":[{"type":"request-id","message":"866e2718-1e73-42f6-8784-8fe5ad24aaa0"}]}
Creating NS1 Record: profile-stage.sooeveningnews.com
::error::Failed to create record: profile-stage.sooeveningnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"2f4d837a-d889-4e11-aa5f-cab2d71a4973"}]}
Creating NS1 Record: profile-stage.southbendtribune.com
::error::Failed to create record: profile-stage.southbendtribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"eeef88ca-79d6-4710-95e4-b7db0c3e2f96"}]}
Creating NS1 Record: profile-stage.southcoasttoday.com
::error::Failed to create record: profile-stage.southcoasttoday.com
{"message":"record already exists","details":[{"type":"request-id","message":"819ed9b3-81fc-4db6-9583-ba331c19c404"}]}
Creating NS1 Record: profile-stage.southernkitchen.com
::error::Failed to create record: profile-stage.southernkitchen.com
{"message":"record already exists","details":[{"type":"request-id","message":"1d542225-b542-475e-9b7c-ed85121a4210"}]}
Creating NS1 Record: profile-stage.spencereveningworld.com
::error::Failed to create record: profile-stage.spencereveningworld.com
{"message":"record already exists","details":[{"type":"request-id","message":"1490a027-72a9-4856-acdc-a93df03420d1"}]}
Creating NS1 Record: profile-stage.starcourier.com
::error::Failed to create record: profile-stage.starcourier.com
{"message":"record already exists","details":[{"type":"request-id","message":"62fb5cd5-5367-47db-97fa-dbba434aa28a"}]}
Creating NS1 Record: profile-stage.stargazette.com
Successfully created record for profile-stage.stargazette.com
Creating NS1 Record: profile-stage.starnewsonline.com
::error::Failed to create record: profile-stage.starnewsonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"c720e34d-164a-4f36-b03a-7ecb95c7ad77"}]}
Creating NS1 Record: profile-stage.statesman.com
::error::Failed to create record: profile-stage.statesman.com
{"message":"record already exists","details":[{"type":"request-id","message":"0fb6ed54-471e-490a-8def-3169508b1801"}]}
Creating NS1 Record: profile-stage.statesmanjournal.com
Successfully created record for profile-stage.statesmanjournal.com
Creating NS1 Record: profile-stage.staugustine.com
::error::Failed to create record: profile-stage.staugustine.com
{"message":"record already exists","details":[{"type":"request-id","message":"5f68717a-4309-4745-ba6a-b7a53f5d8ade"}]}
Creating NS1 Record: profile-stage.stevenspointjournal.com
Successfully created record for profile-stage.stevenspointjournal.com
Creating NS1 Record: profile-stage.storycityherald.com
::error::Failed to create record: profile-stage.storycityherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"5c04f704-01ee-4697-a794-b5b03bfb47c1"}]}
Creating NS1 Record: profile-stage.sturgisjournal.com
::error::Failed to create record: profile-stage.sturgisjournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"aea202ef-2059-420e-8dc2-9e8d2e7acf11"}]}
Creating NS1 Record: profile-stage.sussexcountian.com
::error::Failed to create record: profile-stage.sussexcountian.com
{"message":"record already exists","details":[{"type":"request-id","message":"6fef5c3b-fc69-45b2-bc7c-0573abbc4d1a"}]}
Creating NS1 Record: profile-stage.swtimes.com
::error::Failed to create record: profile-stage.swtimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"9cd6e429-9800-4277-9cb3-fddc254d7f8b"}]}
Creating NS1 Record: profile-stage.tallahassee.com
Successfully created record for profile-stage.tallahassee.com
Creating NS1 Record: profile-stage.tauntongazette.com
::error::Failed to create record: profile-stage.tauntongazette.com
{"message":"record already exists","details":[{"type":"request-id","message":"3d6143c2-97c0-4561-9f98-341672c694fc"}]}
Creating NS1 Record: profile-stage.tcpalm.com
Successfully created record for profile-stage.tcpalm.com
Creating NS1 Record: profile-stage.telegram.com
::error::Failed to create record: profile-stage.telegram.com
{"message":"record already exists","details":[{"type":"request-id","message":"0ccff5c2-6caa-4443-8e25-ef0df9e907e1"}]}
Creating NS1 Record: profile-stage.tennessean.com
Successfully created record for profile-stage.tennessean.com
Creating NS1 Record: profile-stage.the-daily-record.com
::error::Failed to create record: profile-stage.the-daily-record.com
{"message":"record already exists","details":[{"type":"request-id","message":"711e001f-6340-41ed-8bb4-60a3c136102e"}]}
Creating NS1 Record: profile-stage.the-leader.com
::error::Failed to create record: profile-stage.the-leader.com
{"message":"record already exists","details":[{"type":"request-id","message":"2e165cbc-07c7-455a-8bfc-e71ababe1c9e"}]}
Creating NS1 Record: profile-stage.the-review.com
::error::Failed to create record: profile-stage.the-review.com
{"message":"record already exists","details":[{"type":"request-id","message":"b3a9b01e-08ff-443c-a0cd-c7ed4125cf42"}]}
Creating NS1 Record: profile-stage.theadvertiser.com
Successfully created record for profile-stage.theadvertiser.com
Creating NS1 Record: profile-stage.thecalifornian.com
Successfully created record for profile-stage.thecalifornian.com
Creating NS1 Record: profile-stage.thedailyjournal.com
Successfully created record for profile-stage.thedailyjournal.com
Creating NS1 Record: profile-stage.thedailyreporter.com
::error::Failed to create record: profile-stage.thedailyreporter.com
{"message":"record already exists","details":[{"type":"request-id","message":"6149ec20-115b-426a-a390-52b816c406b0"}]}
Creating NS1 Record: profile-stage.thedestinlog.com
::error::Failed to create record: profile-stage.thedestinlog.com
{"message":"record already exists","details":[{"type":"request-id","message":"f73c2888-4b6b-42dc-9a3c-af14d4ec792d"}]}
Creating NS1 Record: profile-stage.thegardnernews.com
::error::Failed to create record: profile-stage.thegardnernews.com
{"message":"record already exists","details":[{"type":"request-id","message":"e6c40012-dacc-4e66-ac42-3440d1754093"}]}
Creating NS1 Record: profile-stage.thegleaner.com
Successfully created record for profile-stage.thegleaner.com
Creating NS1 Record: profile-stage.thehammontonnews.com
Successfully created record for profile-stage.thehammontonnews.com
Creating NS1 Record: profile-stage.theintell.com
::error::Failed to create record: profile-stage.theintell.com
{"message":"record already exists","details":[{"type":"request-id","message":"c5d8b630-8700-46d4-92fc-572f0cb276da"}]}
Creating NS1 Record: profile-stage.theleafchronicle.com
Successfully created record for profile-stage.theleafchronicle.com
Creating NS1 Record: profile-stage.theledger.com
::error::Failed to create record: profile-stage.theledger.com
{"message":"record already exists","details":[{"type":"request-id","message":"58079456-5edb-4ba6-aedc-69bbe3e2ab51"}]}
Creating NS1 Record: profile-stage.thenews-messenger.com
Successfully created record for profile-stage.thenews-messenger.com
Creating NS1 Record: profile-stage.thenewsstar.com
Successfully created record for profile-stage.thenewsstar.com
Creating NS1 Record: profile-stage.thenorthwestern.com
Successfully created record for profile-stage.thenorthwestern.com
Creating NS1 Record: profile-stage.theperrychief.com
::error::Failed to create record: profile-stage.theperrychief.com
{"message":"record already exists","details":[{"type":"request-id","message":"baab6045-0ccc-43c2-881b-6e007e8e3647"}]}
Creating NS1 Record: profile-stage.thepublicopinion.com
::error::Failed to create record: profile-stage.thepublicopinion.com
{"message":"record already exists","details":[{"type":"request-id","message":"b2aedd52-72f4-40d3-b376-dec3f5a33b5f"}]}
Creating NS1 Record: profile-stage.therecordherald.com
::error::Failed to create record: profile-stage.therecordherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"485c2cf5-1447-4149-b52e-882fce72d09d"}]}
Creating NS1 Record: profile-stage.thespectrum.com
Successfully created record for profile-stage.thespectrum.com
Creating NS1 Record: profile-stage.thestarpress.com
Successfully created record for profile-stage.thestarpress.com
Creating NS1 Record: profile-stage.thesuburbanite.com
::error::Failed to create record: profile-stage.thesuburbanite.com
{"message":"record already exists","details":[{"type":"request-id","message":"1f16a06b-a971-41ae-a107-12ff55d62d08"}]}
Creating NS1 Record: profile-stage.thetimesherald.com
Successfully created record for profile-stage.thetimesherald.com
Creating NS1 Record: profile-stage.thetowntalk.com
Successfully created record for profile-stage.thetowntalk.com
Creating NS1 Record: profile-stage.thisweeknews.com
::error::Failed to create record: profile-stage.thisweeknews.com
{"message":"record already exists","details":[{"type":"request-id","message":"c32896ce-4b33-4fc5-adf6-741f5b9d5012"}]}
Creating NS1 Record: profile-stage.tidesports.com
::error::Failed to create record: profile-stage.tidesports.com
{"message":"record already exists","details":[{"type":"request-id","message":"6ace96f6-e469-4056-aba7-db89b2458557"}]}
Creating NS1 Record: profile-stage.times-gazette.com
::error::Failed to create record: profile-stage.times-gazette.com
{"message":"record already exists","details":[{"type":"request-id","message":"3fa6dfd1-8602-4210-b093-b3af3d63ff04"}]}
Creating NS1 Record: profile-stage.timesonline.com
::error::Failed to create record: profile-stage.timesonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"a1930f04-52ce-4e7b-a036-04d4196f60a2"}]}
Creating NS1 Record: profile-stage.timesrecordnews.com
Successfully created record for profile-stage.timesrecordnews.com
Creating NS1 Record: profile-stage.timesreporter.com
::error::Failed to create record: profile-stage.timesreporter.com
{"message":"record already exists","details":[{"type":"request-id","message":"a8b1179f-04fc-48fc-a025-92e0f75d5eb4"}]}
Creating NS1 Record: profile-stage.timestelegram.com
::error::Failed to create record: profile-stage.timestelegram.com
{"message":"record already exists","details":[{"type":"request-id","message":"8c723012-ece7-4611-902e-76698244d3a6"}]}
Creating NS1 Record: profile-stage.tmnews.com
::error::Failed to create record: profile-stage.tmnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"a4585749-6394-4045-b3e0-6d31ab824f62"}]}
Creating NS1 Record: profile-stage.tricountyindependent.com
::error::Failed to create record: profile-stage.tricountyindependent.com
{"message":"record already exists","details":[{"type":"request-id","message":"3e381eb4-33c9-4d5e-b111-f46b7dce530b"}]}
Creating NS1 Record: profile-stage.tricountytimes.com
::error::Failed to create record: profile-stage.tricountytimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"16e84cbe-6a2e-4674-a604-64c3c96ca960"}]}
Creating NS1 Record: profile-stage.tucson.com
Successfully created record for profile-stage.tucson.com
Creating NS1 Record: profile-stage.tuscaloosanews.com
::error::Failed to create record: profile-stage.tuscaloosanews.com
{"message":"record already exists","details":[{"type":"request-id","message":"9eb5b8d3-90e5-42c0-8848-1a69cb5fca3e"}]}
Creating NS1 Record: profile-stage.upstateparent.com
Successfully created record for profile-stage.upstateparent.com
Creating NS1 Record: profile-stage.usatoday.com
::error::Failed to create record: profile-stage.usatoday.com
{"message":"record already exists","details":[{"type":"request-id","message":"18af50e5-e41c-485b-9fb0-d2854782cb65"}]}
Creating NS1 Record: profile-stage.usatodaynetwork.com
Successfully created record for profile-stage.usatodaynetwork.com
Creating NS1 Record: profile-stage.usatodaysportsplus.com
::error::Failed to create record: profile-stage.usatodaysportsplus.com
{"message":"record already exists","details":[{"type":"request-id","message":"242988a6-f637-4ed1-a107-25fdf3e67938"}]}
Creating NS1 Record: profile-stage.uticaod.com
::error::Failed to create record: profile-stage.uticaod.com
{"message":"record already exists","details":[{"type":"request-id","message":"194f539a-0da3-40ca-91ed-6420d4cfa834"}]}
Creating NS1 Record: profile-stage.vcstar.com
Successfully created record for profile-stage.vcstar.com
Creating NS1 Record: profile-stage.visaliatimesdelta.com
Successfully created record for profile-stage.visaliatimesdelta.com
Creating NS1 Record: profile-stage.vvdailypress.com
::error::Failed to create record: profile-stage.vvdailypress.com
{"message":"record already exists","details":[{"type":"request-id","message":"96d822e8-1bab-43ab-8836-faef94672f88"}]}
Creating NS1 Record: profile-stage.waltonsun.com
::error::Failed to create record: profile-stage.waltonsun.com
{"message":"record already exists","details":[{"type":"request-id","message":"5ba1e058-177e-4e6b-8249-0ef30efe8bb8"}]}
Creating NS1 Record: profile-stage.washingtontimesreporter.com
::error::Failed to create record: profile-stage.washingtontimesreporter.com
{"message":"record already exists","details":[{"type":"request-id","message":"c2f4e631-d118-4302-9db6-e742674f93fd"}]}
Creating NS1 Record: profile-stage.wausaudailyherald.com
Successfully created record for profile-stage.wausaudailyherald.com
Creating NS1 Record: profile-stage.waynepost.com
::error::Failed to create record: profile-stage.waynepost.com
{"message":"record already exists","details":[{"type":"request-id","message":"847676f5-331e-4d63-9e6e-d4d949b72977"}]}
Creating NS1 Record: profile-stage.weeklycitizen.com
::error::Failed to create record: profile-stage.weeklycitizen.com
{"message":"record already exists","details":[{"type":"request-id","message":"180839dd-d8b5-4e0a-ae55-13fa54e925e1"}]}
Creating NS1 Record: profile-stage.wellsvilledaily.com
::error::Failed to create record: profile-stage.wellsvilledaily.com
{"message":"record already exists","details":[{"type":"request-id","message":"9c0dd8c3-c821-4cab-b148-eea7c99a7844"}]}
Creating NS1 Record: profile-stage.wickedlocal.com
::error::Failed to create record: profile-stage.wickedlocal.com
{"message":"record already exists","details":[{"type":"request-id","message":"c29b71a4-3d4f-435a-b733-8f9692803a53"}]}
Creating NS1 Record: profile-stage.wisconsinrapidstribune.com
Successfully created record for profile-stage.wisconsinrapidstribune.com
Creating NS1 Record: profile-stage.wisfarmer.com
Successfully created record for profile-stage.wisfarmer.com
Creating NS1 Record: profile-stage.woodfordtimes.com
::error::Failed to create record: profile-stage.woodfordtimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"4f019659-e7c5-4a0a-8f46-54bf2283c435"}]}
Creating NS1 Record: profile-stage.worcestermag.com
::error::Failed to create record: profile-stage.worcestermag.com
{"message":"record already exists","details":[{"type":"request-id","message":"cf93dc4c-39cf-4f4e-9394-c14feb5b9626"}]}
Creating NS1 Record: profile-stage.ydr.com
Successfully created record for profile-stage.ydr.com
Creating NS1 Record: profile-stage.yorkdispatch.com
Successfully created record for profile-stage.yorkdispatch.com
Creating NS1 Record: profile-stage.zanesvilletimesrecorder.com
Successfully created record for profile-stage.zanesvilletimesrecorder.com
