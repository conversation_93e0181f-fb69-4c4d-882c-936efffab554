#!/bin/bash

file="test.json"

arrstr=($(jq -r '.[] | @sh' $file | tr -d \'\"))
len=${#arrstr[@]}
for (( j=0; j<$len; j++ )); do
    page="${arrstr[$j]}"
    if [[ $page != htt* ]]; then
        page="https://${page}"
    fi
    options='--fail -L --connect-timeout 3 --retry 0 -s -o /dev/null -w %{http_code}'
    command="curl -s -L -I -f -o /dev/null -w %{http_code} --connect-timeout 6 ${page}"
    command="curl -s -o /dev/null -w \"%{http_code}\n\" --connect-timeout 6 ${page}"
    echo $command
    httpresponse=0
    retVal=0
    error=0
    httpresponse=$(${command})
    retVal=$?
    # echo "http-code: ($httpresponse) ret-val: ($retVal)"
    if (( retVal != 0 )) ; then
        echo "{\"redirect\": \"${page}\", \"status_code\": \"${retVal}\""
    elif (( httpresponse != 200 )); then
        echo " page: ${page} returned http-code: ${httpresponse} "
    fi
