#!/bin/bash
set -e  # Exit on error

# input_json="CMRev-vcl-cm-go-test2.json"
input_json="CMRev-vcl-cm-go-test3.json"

REQUEST_TYPE="PUT"
RECORDTYPE="CNAME"
COUNTER=0

if [[ ! -f X_NSONE_Key.txt ]]; then
  echo "::error::API key file not found"
  exit 1
fi
X_NSONE_KEY=$(cat X_NSONE_Key.txt)

jq -cr '.[]' $input_json | while read -r i; do
  ZONENAME=$(echo "$i" | rev | cut -d "." -f 1-2 | rev)
  DOMAINNAME="account-ci.${i}"
  #REQUEST_BODY='{"zone": "'$ZONENAME'","domain": "'$DOMAINNAME'","type": "CNAME","answers": [{"answer": ["domains.gannett.map.fastly.net."]}]}'
  REQUEST_BODY="{\"zone\": \"$ZONENAME\",\"domain\": \"$DOMAINNAME\",\"type\": \"CNAME\",\"answers\": [{\"answer\": [\"domains.gannett.map.fastly.net.\"]}]}"

  # echo $REQUEST_BODY
  ### CMRev-vcl-cm-go
  # ci-cm.site.com
  # ci-help.site.com
  # cm-stage.site.com
  # help-stage.site.com
  ### CMRev-vcl-SAM
  # account-stage.site.com
  #account-ci.site.com
  ### CMRev-vcl-onboarding
  #onboarding-staging.site.com
  # ci-onboarding.site.com
  ### CMRev-vcl-profile.site.com
  # ci-profile.site.com
  # profile-stage.site.com
  ### CMRev-vcl-chat
  # chat-stage.site.com
  # ci-chat.site.com

  # account-ci.site.com all markets
  # ci-static.indystar.com
  # staging-static.indystar.com
  # ci-atoms.gannettdigital.com
  # ci-atoms.usatoday.com
  # staging-atoms.gannettdigital.com
  # staging-atoms.usatoday.com

  echo "Creating NS1 Record: $DOMAINNAME"
  response=$(curl -sw "%{http_code}" --request $REQUEST_TYPE \
    --url "https://api.nsone.net/v1/zones/$ZONENAME/$DOMAINNAME/$RECORDTYPE" \
    --header "X-NSONE-Key: $X_NSONE_KEY" \
    --header "accept: application/json" \
    --header "content-type: application/json" \
    --data "$REQUEST_BODY")

  STATUS_CODE=$(tail -n1 <<< "$response")
  BODY=$(sed '$ d' <<< "$response")

  if [[ ! "$STATUS_CODE" =~ ^(200|204)$ ]]; then
    echo "::error::Failed to create record: $DOMAINNAME"
    echo "$BODY"
  else
    echo "Successfully created record for $DOMAINNAME"
  fi

  # COUNTER=$((COUNTER + 1))
  # if [ $COUNTER -gt 5 ]; then
  #   break
  # fi
done