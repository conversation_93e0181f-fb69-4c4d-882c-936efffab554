#!/bin/bash

# Exit on error
set -e

##  curl the fastly api for all the domains and if a tls domains is there it's activated

# Configuration
PAGES=11
PAGE_SIZE=100
OUTPUT_DIR="./domains"

mkdir -p "$OUTPUT_DIR"

# export FASTLY_API_KEY=********************************

FASTLY_API_TOKEN=$(cat FASTLY_API_TOKEN.txt)

if [ -z "$FASTLY_API_TOKEN" ]; then
  echo "Error: FASTLY_API_TOKEN environment variable not set"
  exit 1
fi

cat /dev/null > $OUTPUT_DIR/domains.txt

# Fetch all pages
for PAGE in $(seq 1 $PAGES); do
  echo "Getting Doimains page $PAGE of $PAGES..."

  RESPONSE=$(curl -s \
    -H "Content-Type: application/vnd.api+json" \
    -H "Accept: application/vnd.api+json" \
    -H "Fastly-Key: ${FASTLY_API_TOKEN}" \
    "https://api.fastly.com/domains/v1")

  # Check for API errors
  if echo "$RESPONSE" | jq -e '.errors' > /dev/null; then
    echo "API Error: $(echo "$RESPONSE" | jq -r '.errors[0].detail')"
    exit 1
  fi

  # Save response to file
  echo "$RESPONSE" | jq . > "$OUTPUT_DIR/domains-$PAGE.json"
  echo "$RESPONSE" | jq -r '.data[].id' >> "$OUTPUT_DIR/domains.txt"
done

echo "All TLS domains downloaded successfully to $OUTPUT_DIR/"