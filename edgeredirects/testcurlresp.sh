#!/bin/bash

json_data="uscp-edgeredirects.json"

# echo "$json_data" | jq -r 'to_entries[] | "\(.key) = \(.value)"' | while IFS= read -r line; do
#   echo "$line"
# done

# cat uscp-edgeredirects.json

# declare -A myarray
# cat "$json_data" | jq -r 'to_entries[] | "\(.key) = \(.value)"'

echo "["
while IFS="=" read -r key value
do
    # myarray[$key]="$value"

    status_code=$(curl --write-out %{http_code} --silent --output /dev/null "$value")

    if [[ "$status_code" -gt  399 ]] ; then
        echo "{\"redirect\": \"$value\", \"status_code\": \"$status_code\"}"
    fi

done < <(jq -r 'to_entries | map("\(.key)=\(.value|tostring)") | .[]' $json_data)
echo "]"


# while read repository
# do
#   name=$(echo "$repository" | jq -r .repositoryName)
#   id=$(echo "$repository" | jq -r .repositoryId)
#
#   echo "${id}=${name}"
# done < <(echo "$repositories" | jq -c '.repositories[]')

# jq -cr '.[]' $input_json | while read -r i; do
#    echo $i
# done

# for k in $(jq '. | keys | .[]' $input_file); do
#   echo $k
# done

# mapfile -t json_data < <(jq -r "to_entries[] | \"\(.key)=\(.value|tostring)\"" "$json_data")
# for pair in "${json_data[@]}"; do
#    IFS='=' read -r key value <<< "$pair"
#    echo "$key: $value"
# done
