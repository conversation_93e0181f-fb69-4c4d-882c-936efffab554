#!/bin/bash

## https://developer.ibm.com/apis/catalog/ns1--ibm-ns1-connect-api/api/API--ns1--ibm-ns1-connect-api#requestRedirectCertificate


#  This script takes a list of domains and creates ssl certs for the domain and a wildcard.

#  ** NOTE NS1 has a limit of 10 cert creations per hour
#   so break it up into 10 certs per text file.
#  Helpful tip.  Run once then next tome switch the payload lines.
#  Uncomment one and comment out the other.
#  It creates a log of the file you did and date and time so you can keep track if you have a lot.


NOW=`date +"%Y-%m-%d_%H_%M"`

echo "$1 $NOW" >> ssl-create.log

for D in `cat $1`

do

echo " creating wildcard for $D"

endpoint="https://api.nsone.net/v1/redirect/certificates"
headers="-H 'accept: application/json' -H 'content-type: application/json'"
#####  STAR payload ##
payload='{"domain": "*.'$D'"}'
####  Domain payload  ##
#payload='{"domain": "'$D'"}'
command="curl -X PUT -H 'X-NSONE-Key: $NSONEKEY' $headers --data '$payload' '$endpoint'"
sleep 3
echo $command
eval $command
done
