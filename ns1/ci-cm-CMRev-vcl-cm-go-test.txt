Creating NS1 Record: ci-cm.aberdeennews.com
::error::Failed to create record: ci-cm.aberdeennews.com
{"message":"record already exists","details":[{"type":"request-id","message":"87ec1aa9-468b-4c8b-bc42-99439e067def"}]}
Creating NS1 Record: ci-cm.aberdeennews.com
::error::Failed to create record: ci-cm.aberdeennews.com
{"message":"record already exists","details":[{"type":"request-id","message":"bd027ad4-8c12-4e2f-b835-ac778847d3fc"}]}
Creating NS1 Record: ci-cm.adelnews.com
Successfully created record for ci-cm.adelnews.com
Creating NS1 Record: ci-cm.alamogordonews.com
Successfully created record for ci-cm.alamogordonews.com
Creating NS1 Record: ci-cm.aledotimesrecord.com
Successfully created record for ci-cm.aledotimesrecord.com
Creating NS1 Record: ci-cm.amarillo.com
Successfully created record for ci-cm.amarillo.com
Creating NS1 Record: ci-cm.amestrib.com
::error::Failed to create record: ci-cm.amestrib.com
{"message":"record already exists","details":[{"type":"request-id","message":"afe6181b-19e8-452c-82b6-8e020165b362"}]}
Creating NS1 Record: ci-cm.app.com
Successfully created record for ci-cm.app.com
Creating NS1 Record: ci-cm.argusleader.com
Successfully created record for ci-cm.argusleader.com
Creating NS1 Record: ci-cm.augustachronicle.com
Successfully created record for ci-cm.augustachronicle.com
Creating NS1 Record: ci-cm.austin360.com
Successfully created record for ci-cm.austin360.com
Creating NS1 Record: ci-cm.azcentral.com
Successfully created record for ci-cm.azcentral.com
Creating NS1 Record: ci-cm.barnesville-enterprise.com
Successfully created record for ci-cm.barnesville-enterprise.com
Creating NS1 Record: ci-cm.barnstablepatriot.com
Successfully created record for ci-cm.barnstablepatriot.com
Creating NS1 Record: ci-cm.battlecreekenquirer.com
Successfully created record for ci-cm.battlecreekenquirer.com
Creating NS1 Record: ci-cm.baystateparent.com
Successfully created record for ci-cm.baystateparent.com
Creating NS1 Record: ci-cm.beaconjournal.com
::error::Failed to create record: ci-cm.beaconjournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"2684c3e9-5839-41b3-bb5d-b19dbc79d992"}]}
Creating NS1 Record: ci-cm.blueridgenow.com
Successfully created record for ci-cm.blueridgenow.com
Creating NS1 Record: ci-cm.blufftontoday.com
Successfully created record for ci-cm.blufftontoday.com
Creating NS1 Record: ci-cm.boonevilledemocrat.com
Successfully created record for ci-cm.boonevilledemocrat.com
Creating NS1 Record: ci-cm.buckeyextra.com
Successfully created record for ci-cm.buckeyextra.com
Creating NS1 Record: ci-cm.buckscountycouriertimes.com
Successfully created record for ci-cm.buckscountycouriertimes.com
Creating NS1 Record: ci-cm.bucyrustelegraphforum.com
Successfully created record for ci-cm.bucyrustelegraphforum.com
Creating NS1 Record: ci-cm.burlingtoncountytimes.com
Successfully created record for ci-cm.burlingtoncountytimes.com
Creating NS1 Record: ci-cm.burlingtonfreepress.com
Successfully created record for ci-cm.burlingtonfreepress.com
Creating NS1 Record: ci-cm.caller.com
Successfully created record for ci-cm.caller.com
Creating NS1 Record: ci-cm.cantondailyledger.com
Successfully created record for ci-cm.cantondailyledger.com
Creating NS1 Record: ci-cm.cantonrep.com
Successfully created record for ci-cm.cantonrep.com
Creating NS1 Record: ci-cm.capecodtimes.com
Successfully created record for ci-cm.capecodtimes.com
Creating NS1 Record: ci-cm.charlestonexpress.com
Successfully created record for ci-cm.charlestonexpress.com
Creating NS1 Record: ci-cm.cheboygannews.com
Successfully created record for ci-cm.cheboygannews.com
Creating NS1 Record: ci-cm.chieftain.com
Successfully created record for ci-cm.chieftain.com
Creating NS1 Record: ci-cm.chillicothegazette.com
Successfully created record for ci-cm.chillicothegazette.com
Creating NS1 Record: ci-cm.chillicothetimesbulletin.com
Successfully created record for ci-cm.chillicothetimesbulletin.com
Creating NS1 Record: ci-cm.chronicle-express.com
::error::Failed to create record: ci-cm.chronicle-express.com
{"message":"zone not found: chronicle-express.com","details":[{"type":"request-id","message":"8d66a9ac-e27c-4c57-9998-8d6639850a9e"}]}
Creating NS1 Record: ci-cm.cincinnati.com
::error::Failed to create record: ci-cm.cincinnati.com
{"message":"record already exists","details":[{"type":"request-id","message":"829a8015-e321-4abc-89bd-91df0da4e05c"}]}
Creating NS1 Record: ci-cm.citizen-times.com
Successfully created record for ci-cm.citizen-times.com
Creating NS1 Record: ci-cm.cjonline.com
Successfully created record for ci-cm.cjonline.com
Creating NS1 Record: ci-cm.clarionledger.com
Successfully created record for ci-cm.clarionledger.com
Creating NS1 Record: ci-cm.coloradoan.com
Successfully created record for ci-cm.coloradoan.com
Creating NS1 Record: ci-cm.columbiadailyherald.com
Successfully created record for ci-cm.columbiadailyherald.com
Creating NS1 Record: ci-cm.columbiatribune.com
Successfully created record for ci-cm.columbiatribune.com
Creating NS1 Record: ci-cm.columbusalive.com
Successfully created record for ci-cm.columbusalive.com
Creating NS1 Record: ci-cm.columbusceo.com
Successfully created record for ci-cm.columbusceo.com
Creating NS1 Record: ci-cm.columbusmonthly.com
Successfully created record for ci-cm.columbusmonthly.com
Creating NS1 Record: ci-cm.columbusparent.com
Successfully created record for ci-cm.columbusparent.com
Creating NS1 Record: ci-cm.commercialappeal.com
Successfully created record for ci-cm.commercialappeal.com
Creating NS1 Record: ci-cm.coshoctontribune.com
Successfully created record for ci-cm.coshoctontribune.com
Creating NS1 Record: ci-cm.courier-journal.com
Successfully created record for ci-cm.courier-journal.com
Creating NS1 Record: ci-cm.courier-tribune.com
::error::Failed to create record: ci-cm.courier-tribune.com
{"message":"zone not found: courier-tribune.com","details":[{"type":"request-id","message":"692d760e-74c1-4c71-b43c-46ce9b26086d"}]}
Creating NS1 Record: ci-cm.courierpostonline.com
Successfully created record for ci-cm.courierpostonline.com
Creating NS1 Record: ci-cm.courierpress.com
Successfully created record for ci-cm.courierpress.com
Creating NS1 Record: ci-cm.currentargus.com
Successfully created record for ci-cm.currentargus.com
Creating NS1 Record: ci-cm.daily-jeff.com
Successfully created record for ci-cm.daily-jeff.com
Creating NS1 Record: ci-cm.daily-times.com
Successfully created record for ci-cm.daily-times.com
Creating NS1 Record: ci-cm.dailyamerican.com
Successfully created record for ci-cm.dailyamerican.com
Creating NS1 Record: ci-cm.dailycomet.com
Successfully created record for ci-cm.dailycomet.com
Creating NS1 Record: ci-cm.dailycommercial.com
Successfully created record for ci-cm.dailycommercial.com
Creating NS1 Record: ci-cm.dailyrecord.com
Successfully created record for ci-cm.dailyrecord.com
Creating NS1 Record: ci-cm.dailyworld.com
Successfully created record for ci-cm.dailyworld.com
Creating NS1 Record: ci-cm.dansvilleonline.com
Successfully created record for ci-cm.dansvilleonline.com
Creating NS1 Record: ci-cm.delawareonline.com
Successfully created record for ci-cm.delawareonline.com
Creating NS1 Record: ci-cm.delmarvanow.com
Successfully created record for ci-cm.delmarvanow.com
Creating NS1 Record: ci-cm.demingheadlight.com
::error::Failed to create record: ci-cm.demingheadlight.com
{"message":"zone not found: demingheadlight.com","details":[{"type":"request-id","message":"3e0382f8-845c-4cd2-bd29-ee94b9e0a94a"}]}
Creating NS1 Record: ci-cm.democratandchronicle.com
Successfully created record for ci-cm.democratandchronicle.com
Creating NS1 Record: ci-cm.desertsun.com
Successfully created record for ci-cm.desertsun.com
Creating NS1 Record: ci-cm.desmoinesregister.com
Successfully created record for ci-cm.desmoinesregister.com
Creating NS1 Record: ci-cm.detroitnews.com
Successfully created record for ci-cm.detroitnews.com
Creating NS1 Record: ci-cm.devilslakejournal.com
::error::Failed to create record: ci-cm.devilslakejournal.com
{"message":"zone not found: devilslakejournal.com","details":[{"type":"request-id","message":"fd645c18-f757-4a38-86d9-b5e71456a988"}]}
Creating NS1 Record: ci-cm.dispatch.com
Successfully created record for ci-cm.dispatch.com
Creating NS1 Record: ci-cm.dnj.com
Successfully created record for ci-cm.dnj.com
Creating NS1 Record: ci-cm.donaldsonvillechief.com
Successfully created record for ci-cm.donaldsonvillechief.com
Creating NS1 Record: ci-cm.doverpost.com
Successfully created record for ci-cm.doverpost.com
Creating NS1 Record: ci-cm.eastpeoriatimescourier.com
Successfully created record for ci-cm.eastpeoriatimescourier.com
Creating NS1 Record: ci-cm.echo-pilot.com
Successfully created record for ci-cm.echo-pilot.com
Creating NS1 Record: ci-cm.ellwoodcityledger.com
Successfully created record for ci-cm.ellwoodcityledger.com
Creating NS1 Record: ci-cm.elpasotimes.com
Successfully created record for ci-cm.elpasotimes.com
Creating NS1 Record: ci-cm.elsoldesalinas.com
Successfully created record for ci-cm.elsoldesalinas.com
Creating NS1 Record: ci-cm.enterprisenews.com
Successfully created record for ci-cm.enterprisenews.com
Creating NS1 Record: ci-cm.eveningsun.com
Successfully created record for ci-cm.eveningsun.com
Creating NS1 Record: ci-cm.eveningtribune.com
Successfully created record for ci-cm.eveningtribune.com
Creating NS1 Record: ci-cm.examiner-enterprise.com
Successfully created record for ci-cm.examiner-enterprise.com
Creating NS1 Record: ci-cm.farmforum.net
Successfully created record for ci-cm.farmforum.net
Creating NS1 Record: ci-cm.fayobserver.com
Successfully created record for ci-cm.fayobserver.com
Creating NS1 Record: ci-cm.fdlreporter.com
Successfully created record for ci-cm.fdlreporter.com
Creating NS1 Record: ci-cm.floridatoday.com
Successfully created record for ci-cm.floridatoday.com
Creating NS1 Record: ci-cm.fosters.com
Successfully created record for ci-cm.fosters.com
Creating NS1 Record: ci-cm.freep.com
Successfully created record for ci-cm.freep.com
Creating NS1 Record: ci-cm.gadsdentimes.com
Successfully created record for ci-cm.gadsdentimes.com
Creating NS1 Record: ci-cm.gainesville.com
Successfully created record for ci-cm.gainesville.com
Creating NS1 Record: ci-cm.galesburg.com
Successfully created record for ci-cm.galesburg.com
Creating NS1 Record: ci-cm.gastongazette.com
Successfully created record for ci-cm.gastongazette.com
Creating NS1 Record: ci-cm.gatorsports.com
Successfully created record for ci-cm.gatorsports.com
Creating NS1 Record: ci-cm.geneseorepublic.com
Successfully created record for ci-cm.geneseorepublic.com
Creating NS1 Record: ci-cm.goerie.com
Successfully created record for ci-cm.goerie.com
Creating NS1 Record: ci-cm.gosanangelo.com
Successfully created record for ci-cm.gosanangelo.com
Creating NS1 Record: ci-cm.goupstate.com
Successfully created record for ci-cm.goupstate.com
Creating NS1 Record: ci-cm.greatfallstribune.com
Successfully created record for ci-cm.greatfallstribune.com
Creating NS1 Record: ci-cm.greenbaypressgazette.com
Successfully created record for ci-cm.greenbaypressgazette.com
Creating NS1 Record: ci-cm.greenvilleonline.com
Successfully created record for ci-cm.greenvilleonline.com
Creating NS1 Record: ci-cm.hattiesburgamerican.com
Successfully created record for ci-cm.hattiesburgamerican.com
Creating NS1 Record: ci-cm.heraldmailmedia.com
Successfully created record for ci-cm.heraldmailmedia.com
Creating NS1 Record: ci-cm.heraldnews.com
Successfully created record for ci-cm.heraldnews.com
Creating NS1 Record: ci-cm.heraldtimesonline.com
Successfully created record for ci-cm.heraldtimesonline.com
Creating NS1 Record: ci-cm.heraldtribune.com
Successfully created record for ci-cm.heraldtribune.com
Creating NS1 Record: ci-cm.hillsdale.net
Successfully created record for ci-cm.hillsdale.net
Creating NS1 Record: ci-cm.hockessincommunitynews.com
Successfully created record for ci-cm.hockessincommunitynews.com
Creating NS1 Record: ci-cm.hollandsentinel.com
Successfully created record for ci-cm.hollandsentinel.com
Creating NS1 Record: ci-cm.hometownlife.com
Successfully created record for ci-cm.hometownlife.com
Creating NS1 Record: ci-cm.hookem.com
Successfully created record for ci-cm.hookem.com
Creating NS1 Record: ci-cm.houmatoday.com
Successfully created record for ci-cm.houmatoday.com
Creating NS1 Record: ci-cm.htrnews.com
Successfully created record for ci-cm.htrnews.com
Creating NS1 Record: ci-cm.hutchnews.com
Successfully created record for ci-cm.hutchnews.com
Creating NS1 Record: ci-cm.indeonline.com
Successfully created record for ci-cm.indeonline.com
Creating NS1 Record: ci-cm.independentmail.com
::error::Failed to create record: ci-cm.independentmail.com
{"message":"record already exists","details":[{"type":"request-id","message":"4f706520-edb5-4c6a-8b56-8c5304aaa70d"}]}
Creating NS1 Record: ci-cm.indystar.com
::error::Failed to create record: ci-cm.indystar.com
{"message":"record already exists","details":[{"type":"request-id","message":"d1ab2406-df4e-4a64-a906-bdc24716a244"}]}
Creating NS1 Record: ci-cm.ithacajournal.com
Successfully created record for ci-cm.ithacajournal.com
Creating NS1 Record: ci-cm.jacksonsun.com
Successfully created record for ci-cm.jacksonsun.com
Creating NS1 Record: ci-cm.jacksonville.com
Successfully created record for ci-cm.jacksonville.com
Creating NS1 Record: ci-cm.jconline.com
Successfully created record for ci-cm.jconline.com
Creating NS1 Record: ci-cm.jdnews.com
Successfully created record for ci-cm.jdnews.com
Creating NS1 Record: ci-cm.journalstandard.com
Successfully created record for ci-cm.journalstandard.com
Creating NS1 Record: ci-cm.jsonline.com
Successfully created record for ci-cm.jsonline.com
Creating NS1 Record: ci-cm.kinston.com
::error::Failed to create record: ci-cm.kinston.com
{"message":"zone not found: kinston.com","details":[{"type":"request-id","message":"33601364-63a3-47ef-ac77-3eeb37d674f6"}]}
Creating NS1 Record: ci-cm.kitsapsun.com
Successfully created record for ci-cm.kitsapsun.com
Creating NS1 Record: ci-cm.knoxnews.com
Successfully created record for ci-cm.knoxnews.com
Creating NS1 Record: ci-cm.lancastereaglegazette.com
Successfully created record for ci-cm.lancastereaglegazette.com
Creating NS1 Record: ci-cm.lansingstatejournal.com
Successfully created record for ci-cm.lansingstatejournal.com
Creating NS1 Record: ci-cm.lcsun-news.com
Successfully created record for ci-cm.lcsun-news.com
Creating NS1 Record: ci-cm.ldnews.com
Successfully created record for ci-cm.ldnews.com
Creating NS1 Record: ci-cm.lenconnect.com
Successfully created record for ci-cm.lenconnect.com
Creating NS1 Record: ci-cm.leominsterchamp.com
::error::Failed to create record: ci-cm.leominsterchamp.com
{"message":"zone not found: leominsterchamp.com","details":[{"type":"request-id","message":"2b2a9f88-f915-4ea1-9618-bf56c7f6d9ae"}]}
Creating NS1 Record: ci-cm.lincolncourier.com
Successfully created record for ci-cm.lincolncourier.com
Creating NS1 Record: ci-cm.linkbostonhomes.com
Successfully created record for ci-cm.linkbostonhomes.com
Creating NS1 Record: ci-cm.livingstondaily.com
Successfully created record for ci-cm.livingstondaily.com
Creating NS1 Record: ci-cm.lohud.com
Successfully created record for ci-cm.lohud.com
Creating NS1 Record: ci-cm.lubbockonline.com
Successfully created record for ci-cm.lubbockonline.com
Creating NS1 Record: ci-cm.mansfieldnewsjournal.com
Successfully created record for ci-cm.mansfieldnewsjournal.com
Creating NS1 Record: ci-cm.marionstar.com
Successfully created record for ci-cm.marionstar.com
Creating NS1 Record: ci-cm.marshfieldnewsherald.com
Successfully created record for ci-cm.marshfieldnewsherald.com
Creating NS1 Record: ci-cm.mcdonoughvoice.com
Successfully created record for ci-cm.mcdonoughvoice.com
Creating NS1 Record: ci-cm.metrowestdailynews.com
Successfully created record for ci-cm.metrowestdailynews.com
Creating NS1 Record: ci-cm.middletowntranscript.com
Successfully created record for ci-cm.middletowntranscript.com
Creating NS1 Record: ci-cm.midlothianmirror.com
::error::Failed to create record: ci-cm.midlothianmirror.com
{"message":"zone not found: midlothianmirror.com","details":[{"type":"request-id","message":"fa3013a0-a027-4d24-99b6-8aafdbd3b06c"}]}
Creating NS1 Record: ci-cm.milfordbeacon.com
Successfully created record for ci-cm.milfordbeacon.com
Creating NS1 Record: ci-cm.milforddailynews.com
Successfully created record for ci-cm.milforddailynews.com
Creating NS1 Record: ci-cm.millburysutton.com
::error::Failed to create record: ci-cm.millburysutton.com
{"message":"zone not found: millburysutton.com","details":[{"type":"request-id","message":"eb63a24a-5faa-4e14-8f44-ca351f0a3d39"}]}
Creating NS1 Record: ci-cm.monroecopost.com
Successfully created record for ci-cm.monroecopost.com
Creating NS1 Record: ci-cm.monroenews.com
Successfully created record for ci-cm.monroenews.com
Creating NS1 Record: ci-cm.montgomeryadvertiser.com
Successfully created record for ci-cm.montgomeryadvertiser.com
Creating NS1 Record: ci-cm.mortontimesnews.com
Successfully created record for ci-cm.mortontimesnews.com
Creating NS1 Record: ci-cm.mpnnow.com
Successfully created record for ci-cm.mpnnow.com
Creating NS1 Record: ci-cm.mtshastanews.com
Successfully created record for ci-cm.mtshastanews.com
Creating NS1 Record: ci-cm.mycentraljersey.com
Successfully created record for ci-cm.mycentraljersey.com
Creating NS1 Record: ci-cm.mytownneo.com
Successfully created record for ci-cm.mytownneo.com
Creating NS1 Record: ci-cm.naplesnews.com
::error::Failed to create record: ci-cm.naplesnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"0c08ca22-a25f-496d-8194-9abbb0908746"}]}
Creating NS1 Record: ci-cm.ndinsider.com
Successfully created record for ci-cm.ndinsider.com
Creating NS1 Record: ci-cm.nevadaiowajournal.com
Successfully created record for ci-cm.nevadaiowajournal.com
Creating NS1 Record: ci-cm.newarkadvocate.com
Successfully created record for ci-cm.newarkadvocate.com
Creating NS1 Record: ci-cm.newbernsj.com
::error::Failed to create record: ci-cm.newbernsj.com
{"message":"zone not found: newbernsj.com","details":[{"type":"request-id","message":"624dbfe2-2d59-452a-806f-4395c4c0ad07"}]}
Creating NS1 Record: ci-cm.newportri.com
Successfully created record for ci-cm.newportri.com
Creating NS1 Record: ci-cm.news-journalonline.com
Successfully created record for ci-cm.news-journalonline.com
Creating NS1 Record: ci-cm.news-leader.com
Successfully created record for ci-cm.news-leader.com
Creating NS1 Record: ci-cm.news-press.com
Successfully created record for ci-cm.news-press.com
Creating NS1 Record: ci-cm.newschief.com
Successfully created record for ci-cm.newschief.com
Creating NS1 Record: ci-cm.newsherald.com
Successfully created record for ci-cm.newsherald.com
Creating NS1 Record: ci-cm.newsleader.com
Successfully created record for ci-cm.newsleader.com
Creating NS1 Record: ci-cm.newsrepublican.com
Successfully created record for ci-cm.newsrepublican.com
Creating NS1 Record: ci-cm.njherald.com
Successfully created record for ci-cm.njherald.com
Creating NS1 Record: ci-cm.northjersey.com
Successfully created record for ci-cm.northjersey.com
Creating NS1 Record: ci-cm.norwichbulletin.com
Successfully created record for ci-cm.norwichbulletin.com
Creating NS1 Record: ci-cm.nwfdailynews.com
Successfully created record for ci-cm.nwfdailynews.com
Creating NS1 Record: ci-cm.oakridger.com
Successfully created record for ci-cm.oakridger.com
Creating NS1 Record: ci-cm.ocala.com
Successfully created record for ci-cm.ocala.com
Creating NS1 Record: ci-cm.oklahoman.com
Successfully created record for ci-cm.oklahoman.com
Creating NS1 Record: ci-cm.onlineathens.com
Successfully created record for ci-cm.onlineathens.com
Creating NS1 Record: ci-cm.pal-item.com
Successfully created record for ci-cm.pal-item.com
Creating NS1 Record: ci-cm.palmbeachdailynews.com
Successfully created record for ci-cm.palmbeachdailynews.com
Creating NS1 Record: ci-cm.palmbeachpost.com
Successfully created record for ci-cm.palmbeachpost.com
Creating NS1 Record: ci-cm.paris-express.com
Successfully created record for ci-cm.paris-express.com
Creating NS1 Record: ci-cm.patriotledger.com
Successfully created record for ci-cm.patriotledger.com
Creating NS1 Record: ci-cm.pawhuskajournalcapital.com
Successfully created record for ci-cm.pawhuskajournalcapital.com
Creating NS1 Record: ci-cm.pekintimes.com
Successfully created record for ci-cm.pekintimes.com
Creating NS1 Record: ci-cm.petoskeynews.com
Successfully created record for ci-cm.petoskeynews.com
Creating NS1 Record: ci-cm.phillyburbs.com
Successfully created record for ci-cm.phillyburbs.com
Creating NS1 Record: ci-cm.pjstar.com
Successfully created record for ci-cm.pjstar.com
Creating NS1 Record: ci-cm.pnj.com
Successfully created record for ci-cm.pnj.com
Creating NS1 Record: ci-cm.poconorecord.com
Successfully created record for ci-cm.poconorecord.com
Creating NS1 Record: ci-cm.pontiacdailyleader.com
Successfully created record for ci-cm.pontiacdailyleader.com
Creating NS1 Record: ci-cm.portclintonnewsherald.com
Successfully created record for ci-cm.portclintonnewsherald.com
Creating NS1 Record: ci-cm.postcrescent.com
Successfully created record for ci-cm.postcrescent.com
Creating NS1 Record: ci-cm.postsouth.com
Successfully created record for ci-cm.postsouth.com
Creating NS1 Record: ci-cm.poughkeepsiejournal.com
Successfully created record for ci-cm.poughkeepsiejournal.com
Creating NS1 Record: ci-cm.press-citizen.com
Successfully created record for ci-cm.press-citizen.com
Creating NS1 Record: ci-cm.pressargus.com
Successfully created record for ci-cm.pressargus.com
Creating NS1 Record: ci-cm.pressconnects.com
Successfully created record for ci-cm.pressconnects.com
Creating NS1 Record: ci-cm.progress-index.com
Successfully created record for ci-cm.progress-index.com
Creating NS1 Record: ci-cm.prosperpressnews.com
::error::Failed to create record: ci-cm.prosperpressnews.com
{"message":"zone not found: prosperpressnews.com","details":[{"type":"request-id","message":"343e0d44-929f-47f1-8890-08919c75a101"}]}
Creating NS1 Record: ci-cm.providencejournal.com
Successfully created record for ci-cm.providencejournal.com
Creating NS1 Record: ci-cm.publicopiniononline.com
Successfully created record for ci-cm.publicopiniononline.com
Creating NS1 Record: ci-cm.record-courier.com
Successfully created record for ci-cm.record-courier.com
Creating NS1 Record: ci-cm.recordnet.com
Successfully created record for ci-cm.recordnet.com
Creating NS1 Record: ci-cm.recordonline.com
Successfully created record for ci-cm.recordonline.com
Creating NS1 Record: ci-cm.recordstar.com
Successfully created record for ci-cm.recordstar.com
Creating NS1 Record: ci-cm.redding.com
Successfully created record for ci-cm.redding.com
Creating NS1 Record: ci-cm.registerguard.com
Successfully created record for ci-cm.registerguard.com
Creating NS1 Record: ci-cm.reporter-times.com
Successfully created record for ci-cm.reporter-times.com
Creating NS1 Record: ci-cm.reporternews.com
Successfully created record for ci-cm.reporternews.com
Creating NS1 Record: ci-cm.reviewatlas.com
Successfully created record for ci-cm.reviewatlas.com
Creating NS1 Record: ci-cm.rgj.com
Successfully created record for ci-cm.rgj.com
Creating NS1 Record: ci-cm.rrstar.com
Successfully created record for ci-cm.rrstar.com
Creating NS1 Record: ci-cm.ruidosonews.com
Successfully created record for ci-cm.ruidosonews.com
Creating NS1 Record: ci-cm.runnelscountyregister.com
::error::Failed to create record: ci-cm.runnelscountyregister.com
{"message":"zone not found: runnelscountyregister.com","details":[{"type":"request-id","message":"889e7864-4e11-459f-a973-b0471291f934"}]}
Creating NS1 Record: ci-cm.salina.com
Successfully created record for ci-cm.salina.com
Creating NS1 Record: ci-cm.savannahnow.com
Successfully created record for ci-cm.savannahnow.com
Creating NS1 Record: ci-cm.scsun-news.com
::error::Failed to create record: ci-cm.scsun-news.com
{"message":"zone not found: scsun-news.com","details":[{"type":"request-id","message":"44688cb8-e137-47ce-bc06-b68d78369a11"}]}
Creating NS1 Record: ci-cm.scsuntimes.com
Successfully created record for ci-cm.scsuntimes.com
Creating NS1 Record: ci-cm.sctimes.com
Successfully created record for ci-cm.sctimes.com
Creating NS1 Record: ci-cm.seacoastonline.com
Successfully created record for ci-cm.seacoastonline.com
Creating NS1 Record: ci-cm.sentinel-standard.com
Successfully created record for ci-cm.sentinel-standard.com
Creating NS1 Record: ci-cm.sheboyganpress.com
Successfully created record for ci-cm.sheboyganpress.com
Creating NS1 Record: ci-cm.shelbystar.com
Successfully created record for ci-cm.shelbystar.com
Creating NS1 Record: ci-cm.shreveporttimes.com
Successfully created record for ci-cm.shreveporttimes.com
Creating NS1 Record: ci-cm.siskiyoudaily.com
Successfully created record for ci-cm.siskiyoudaily.com
Creating NS1 Record: ci-cm.sj-r.com
Successfully created record for ci-cm.sj-r.com
Creating NS1 Record: ci-cm.sooeveningnews.com
Successfully created record for ci-cm.sooeveningnews.com
Creating NS1 Record: ci-cm.southbendtribune.com
Successfully created record for ci-cm.southbendtribune.com
Creating NS1 Record: ci-cm.southcoasttoday.com
Successfully created record for ci-cm.southcoasttoday.com
Creating NS1 Record: ci-cm.southernkitchen.com
Successfully created record for ci-cm.southernkitchen.com
Creating NS1 Record: ci-cm.spencereveningworld.com
Successfully created record for ci-cm.spencereveningworld.com
Creating NS1 Record: ci-cm.starcourier.com
Successfully created record for ci-cm.starcourier.com
Creating NS1 Record: ci-cm.stargazette.com
Successfully created record for ci-cm.stargazette.com
Creating NS1 Record: ci-cm.starnewsonline.com
Successfully created record for ci-cm.starnewsonline.com
Creating NS1 Record: ci-cm.statesman.com
Successfully created record for ci-cm.statesman.com
Creating NS1 Record: ci-cm.statesmanjournal.com
Successfully created record for ci-cm.statesmanjournal.com
Creating NS1 Record: ci-cm.staugustine.com
Successfully created record for ci-cm.staugustine.com
Creating NS1 Record: ci-cm.steubencourier.com
::error::Failed to create record: ci-cm.steubencourier.com
{"message":"zone not found: steubencourier.com","details":[{"type":"request-id","message":"57ba7efd-53b5-4224-b569-da53015c2785"}]}
Creating NS1 Record: ci-cm.stevenspointjournal.com
Successfully created record for ci-cm.stevenspointjournal.com
Creating NS1 Record: ci-cm.storycityherald.com
Successfully created record for ci-cm.storycityherald.com
Creating NS1 Record: ci-cm.sturgisjournal.com
Successfully created record for ci-cm.sturgisjournal.com
Creating NS1 Record: ci-cm.sussexcountian.com
Successfully created record for ci-cm.sussexcountian.com
Creating NS1 Record: ci-cm.swtimes.com
Successfully created record for ci-cm.swtimes.com
Creating NS1 Record: ci-cm.tallahassee.com
Successfully created record for ci-cm.tallahassee.com
Creating NS1 Record: ci-cm.tauntongazette.com
Successfully created record for ci-cm.tauntongazette.com
Creating NS1 Record: ci-cm.tcpalm.com
Successfully created record for ci-cm.tcpalm.com
Creating NS1 Record: ci-cm.telegram.com
Successfully created record for ci-cm.telegram.com
Creating NS1 Record: ci-cm.tennessean.com
Successfully created record for ci-cm.tennessean.com
Creating NS1 Record: ci-cm.the-daily-record.com
Successfully created record for ci-cm.the-daily-record.com
Creating NS1 Record: ci-cm.the-dispatch.com
::error::Failed to create record: ci-cm.the-dispatch.com
{"message":"zone not found: the-dispatch.com","details":[{"type":"request-id","message":"afbbe5ba-0694-4953-8beb-e6853eb0e1ed"}]}
Creating NS1 Record: ci-cm.the-leader.com
Successfully created record for ci-cm.the-leader.com
Creating NS1 Record: ci-cm.the-review.com
Successfully created record for ci-cm.the-review.com
Creating NS1 Record: ci-cm.theadvertiser.com
Successfully created record for ci-cm.theadvertiser.com
Creating NS1 Record: ci-cm.thecalifornian.com
Successfully created record for ci-cm.thecalifornian.com
Creating NS1 Record: ci-cm.thedailyjournal.com
Successfully created record for ci-cm.thedailyjournal.com
Creating NS1 Record: ci-cm.thedailyreporter.com
Successfully created record for ci-cm.thedailyreporter.com
Creating NS1 Record: ci-cm.thedestinlog.com
Successfully created record for ci-cm.thedestinlog.com
Creating NS1 Record: ci-cm.thegardnernews.com
Successfully created record for ci-cm.thegardnernews.com
Creating NS1 Record: ci-cm.thegleaner.com
Successfully created record for ci-cm.thegleaner.com
Creating NS1 Record: ci-cm.thegraftonnews.com
::error::Failed to create record: ci-cm.thegraftonnews.com
{"message":"zone not found: thegraftonnews.com","details":[{"type":"request-id","message":"05c33eef-2017-4380-812d-5c42ddb3d107"}]}
Creating NS1 Record: ci-cm.thehawkeye.com
::error::Failed to create record: ci-cm.thehawkeye.com
{"message":"zone not found: thehawkeye.com","details":[{"type":"request-id","message":"5ccbd797-4b37-4885-8128-58b9532103a5"}]}
Creating NS1 Record: ci-cm.theintell.com
Successfully created record for ci-cm.theintell.com
Creating NS1 Record: ci-cm.thelandmark.com
::error::Failed to create record: ci-cm.thelandmark.com
{"message":"zone not found: thelandmark.com","details":[{"type":"request-id","message":"11d5172b-6476-46bb-b5b6-cd6388e26b61"}]}
Creating NS1 Record: ci-cm.theleafchronicle.com
Successfully created record for ci-cm.theleafchronicle.com
Creating NS1 Record: ci-cm.theledger.com
Successfully created record for ci-cm.theledger.com
Creating NS1 Record: ci-cm.thenews-messenger.com
Successfully created record for ci-cm.thenews-messenger.com
Creating NS1 Record: ci-cm.thenewsstar.com
Successfully created record for ci-cm.thenewsstar.com
Creating NS1 Record: ci-cm.thenorthwestern.com
Successfully created record for ci-cm.thenorthwestern.com
Creating NS1 Record: ci-cm.theperrychief.com
Successfully created record for ci-cm.theperrychief.com
Creating NS1 Record: ci-cm.thepublicopinion.com
Successfully created record for ci-cm.thepublicopinion.com
Creating NS1 Record: ci-cm.therecordherald.com
Successfully created record for ci-cm.therecordherald.com
Creating NS1 Record: ci-cm.thespectrum.com
Successfully created record for ci-cm.thespectrum.com
Creating NS1 Record: ci-cm.thestarpress.com
Successfully created record for ci-cm.thestarpress.com
Creating NS1 Record: ci-cm.thesuburbanite.com
Successfully created record for ci-cm.thesuburbanite.com
Creating NS1 Record: ci-cm.thetimesherald.com
Successfully created record for ci-cm.thetimesherald.com
Creating NS1 Record: ci-cm.thetimesnews.com
::error::Failed to create record: ci-cm.thetimesnews.com
{"message":"zone not found: thetimesnews.com","details":[{"type":"request-id","message":"8495af35-4f6f-45d5-ac41-796f0c3db33a"}]}
Creating NS1 Record: ci-cm.thetowntalk.com
Successfully created record for ci-cm.thetowntalk.com
Creating NS1 Record: ci-cm.thisweeknews.com
Successfully created record for ci-cm.thisweeknews.com
Creating NS1 Record: ci-cm.tidesports.com
Successfully created record for ci-cm.tidesports.com
Creating NS1 Record: ci-cm.times-gazette.com
Successfully created record for ci-cm.times-gazette.com
Creating NS1 Record: ci-cm.timesonline.com
Successfully created record for ci-cm.timesonline.com
Creating NS1 Record: ci-cm.timesrecordnews.com
Successfully created record for ci-cm.timesrecordnews.com
Creating NS1 Record: ci-cm.timesreporter.com
Successfully created record for ci-cm.timesreporter.com
Creating NS1 Record: ci-cm.timestelegram.com
Successfully created record for ci-cm.timestelegram.com
Creating NS1 Record: ci-cm.tmnews.com
Successfully created record for ci-cm.tmnews.com
Creating NS1 Record: ci-cm.tricountyindependent.com
Successfully created record for ci-cm.tricountyindependent.com
Creating NS1 Record: ci-cm.tricountytimes.com
Successfully created record for ci-cm.tricountytimes.com
Creating NS1 Record: ci-cm.tuscaloosanews.com
Successfully created record for ci-cm.tuscaloosanews.com
Creating NS1 Record: ci-cm.usatoday.com
::error::Failed to create record: ci-cm.usatoday.com
{"message":"record already exists","details":[{"type":"request-id","message":"608fc1c2-ec41-42dc-bc50-ba41a279696e"}]}
Creating NS1 Record: ci-cm.usatodaysportsplus.com
Successfully created record for ci-cm.usatodaysportsplus.com
Creating NS1 Record: ci-cm.uticaod.com
Successfully created record for ci-cm.uticaod.com
Creating NS1 Record: ci-cm.vanalstyneleader.com
::error::Failed to create record: ci-cm.vanalstyneleader.com
{"message":"zone not found: vanalstyneleader.com","details":[{"type":"request-id","message":"9d6b7b3a-4072-4079-b20e-783d573b0046"}]}
Creating NS1 Record: ci-cm.vcstar.com
Successfully created record for ci-cm.vcstar.com
Creating NS1 Record: ci-cm.visaliatimesdelta.com
Successfully created record for ci-cm.visaliatimesdelta.com
Creating NS1 Record: ci-cm.vvdailypress.com
Successfully created record for ci-cm.vvdailypress.com
Creating NS1 Record: ci-cm.waltonsun.com
Successfully created record for ci-cm.waltonsun.com
Creating NS1 Record: ci-cm.washingtontimesreporter.com
Successfully created record for ci-cm.washingtontimesreporter.com
Creating NS1 Record: ci-cm.wausaudailyherald.com
Successfully created record for ci-cm.wausaudailyherald.com
Creating NS1 Record: ci-cm.waynepost.com
Successfully created record for ci-cm.waynepost.com
Creating NS1 Record: ci-cm.weeklycitizen.com
Successfully created record for ci-cm.weeklycitizen.com
Creating NS1 Record: ci-cm.wellsvilledaily.com
Successfully created record for ci-cm.wellsvilledaily.com
Creating NS1 Record: ci-cm.wickedlocal.com
Successfully created record for ci-cm.wickedlocal.com
Creating NS1 Record: ci-cm.wisconsinrapidstribune.com
Successfully created record for ci-cm.wisconsinrapidstribune.com
Creating NS1 Record: ci-cm.woodfordtimes.com
Successfully created record for ci-cm.woodfordtimes.com
Creating NS1 Record: ci-cm.worcestermag.com
Successfully created record for ci-cm.worcestermag.com
Creating NS1 Record: ci-cm.ydr.com
::error::Failed to create record: ci-cm.ydr.com
{"message":"record already exists","details":[{"type":"request-id","message":"3b6990fa-a7c4-4894-93f5-850be6b03d85"}]}
Creating NS1 Record: ci-cm.yorkdispatch.com
Successfully created record for ci-cm.yorkdispatch.com
Creating NS1 Record: ci-cm.yourglenrosetx.com
::error::Failed to create record: ci-cm.yourglenrosetx.com
{"message":"zone not found: yourglenrosetx.com","details":[{"type":"request-id","message":"bb34101e-e378-4641-bd09-b93849a5b985"}]}
Creating NS1 Record: ci-cm.zanesvilletimesrecorder.com
Successfully created record for ci-cm.zanesvilletimesrecorder.com
