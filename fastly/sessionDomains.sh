#!/bin/bash

# Exit on error
set -e

##  curl the fastly api for all the domains and if a tls domains is there it's activated

# Configuration
PAGES=100
PAGE_SIZE=1
OUTPUT_DIR="./sessions"

mkdir -p "$OUTPUT_DIR"

# export FASTLY_API_KEY=********************************

FASTLY_API_TOKEN=$(cat FASTLY_API_TOKEN.txt)

if [ -z "$FASTLY_API_TOKEN" ]; then
  echo "Error: FASTLY_API_TOKEN environment variable not set"
  exit 1
fi

cat /dev/null > $OUTPUT_DIR/sessions.txt

# NEXT_URL="https://api.fastly.com/service?direction=ascend&page=1&per_page=$PAGE_SIZE&sort=created&include=tls_domains"
#NEXT_URL="https://api.fastly.com/services?include=domains&page[number]=1&page[size]=1"
NEXT_URL="https://api.fastly.com/services?include=domains&page%5Bnumber%5D=1&page%5Bsize%5D=10"
PAGE=1

# Fetch all pages
while [ -n "$NEXT_URL" ] && [ "$PAGE" -le "$PAGES" ]; do
  echo "Getting Doimains page $PAGE of $PAGES..."

  RESPONSE=$(curl -s \
    -H "Accept: application/json" \
    -H "Fastly-Key: ${FASTLY_API_TOKEN}" \
    "${NEXT_URL}")

  # Check for API errors
  if echo "$RESPONSE" | jq -e '.msg' > /dev/null; then
    echo "API Error: $(echo "$RESPONSE" | jq -r '.msg')"
    exit 1
  fi

  echo "response: $RESPONSE"

  #echo "$RESPONSE" | jq -r '.[].id' >> "$OUTPUT_DIR/sessions.txt"
  echo "$RESPONSE" | jq -r '.data[].relationships.domains.data[].id' >> "$OUTPUT_DIR/sessions.txt"

  # Get next page URL
  NEXT_URL=$(echo "$RESPONSE" | jq -r '.links.next')

  echo "linknext: $NEXT_URL"

    # Break if no more pages or empty link
  if [ "$NEXT_URL" == "null" ] || [ -z "$NEXT_URL" ]; then
    echo "No more pages to fetch."
    break
  fi

  PAGE=$((PAGE+1))
done

echo "All TLS domains downloaded successfully to $OUTPUT_DIR/"