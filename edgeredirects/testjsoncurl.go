package main

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
)

type Output struct {
	Redirect   string `json:"redirect"`
	StatusCode string `json:"status_code"`
	Error      string `json:"error"`
}

var output []Output

func StatusCode(PAGE string, AUTH string) (r int, err error) {
	// Setup the request.

	http.DefaultTransport.(*http.Transport).TLSClientConfig = &tls.Config{InsecureSkipVerify: true}
	req, err := http.NewRequest("GET", PAGE, nil)
	if err != nil {
		// println(err.Error())
		return 600, err
	}

	if AUTH != "" {
		req.Header.Set("Authorization", AUTH)
	}

	// Execute the request.
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		// println(err.Error())
		return 700, err
	}

	// Close response body as required.
	defer resp.Body.Close()

	// fmt.Println("HTTP Response Status:", resp.StatusCode, http.StatusText(resp.StatusCode))
	return resp.StatusCode, nil
	// or fmt.Sprintf("%d %s", resp.StatusCode, http.StatusText(resp.StatusCode))
}

func main() {
	content, err := os.ReadFile("./uscp-edgeredirects.json")
	if err != nil {
		log.Fatal("Error when opening file: ", err)
	}

	payload := make(map[string]string)
	err = json.Unmarshal(content, &payload)
	if err != nil {
		log.Printf("ERROR: fail to unmarshla json, %s", err.Error())
	}
	// log.Printf("INFO: jsonMap, %s", payload)

	cnt := 0
	for k, v := range payload {
		// fmt.Printf("cnt: %d, Key: %s, Value: %s\n", cnt, k, v)
		status_code, err := StatusCode(v, "Basic YWRtaW46NHRlb3J0c3RyK3JwZTI=")

		if status_code >= 400 {
			error := ""
			if err != nil {
				error = err.Error()
			}
			fmt.Printf("Key: %s, Value: %s, Status: %d, Error: %s\n", k, v, status_code, error)
			Output := Output{Redirect: v, StatusCode: strconv.Itoa(status_code), Error: error}
			output = append(output, Output)
		}
		cnt++
	}
	fmt.Printf("Total: %d\n", cnt)

	jsonOutput, err := json.Marshal(output)
	if err != nil {
		log.Fatal("Error marshaling JSON:", err)
	}

	// Write the JSON output to a file
	err = os.WriteFile("out.json", jsonOutput, 0644)
	if err != nil {
		log.Fatal("Error writing JSON file:", err)
	}
}
