#!/bin/bash

# Input: service_id and acl_id grouped on lines
service_acl_list="4wGGHP7A9F10KuUpHDEE82,F0ISEmHtfwr5hTGmsGEIo6
2C3XRHp8pBEsq8N1A7YDRh,IrHv8wo5N9UkSM00y6rIk5"

# Define the data to be posted
    data='{"ip": "***********", "subnet": "24", "comment": "test of script"}'

input=$1

while IFS= read -r line
do

  IFS=, read -r SVC_ID ACL_ID <<<"$line"


  echo "$SVC_ID $ACL_ID"

    # Make the POST request
  #echo  curl -X POST -H "Fastly-Key: ${FASTLY_API_KEY}" https://api.fastly.com/service/$SVC_ID/acl/$ACL_ID/entry -H "Content-Type: application/json" -d "$data"


#curl -s -H "Fastly-Key: ${FASTLY_API_KEY}" https://api.fastly.com/service/$SVC_ID/acl/$ACL_ID/entries -H "Accept: application/json" | jq '.'
  response=$(curl -s -H "Fastly-Key: ${FASTLY_API_KEY}" https://api.fastly.com/service/$SVC_ID/acl/$ACL_ID/entries -H "Accept: application/json" )

      CODE=$(tail -n1 <<< "$response")
      BODY=$(sed '$ d' <<< "$response")
      if [ $CODE != "200" -a $CODE != "204" ]; then
      echo "::error::Webhook trigger failed"
      echo "$BODY"
      echo "$CODE"
      echo " fail $SVC_ID  ---------------------"
      exit 1
    fi

  printf $response
echo " success ---------------------"
done < "$input"