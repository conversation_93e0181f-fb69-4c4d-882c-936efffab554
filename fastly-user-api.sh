# Make sure you have FASTLY_API_KEY

# take email address as input
email=$1
USER_EMAIL=$(echo "\"$email\"" | tr '[:upper:]' '[:lower:]')

# Search all Fastly users to grab the User id, role, limit_services value based on the email
USER_INFO=$(curl --location --request GET "https://api.fastly.com/customer/7Dnlnz2WAOxMzGFLXGxaYl/users" --header "Fastly-Key: $FASTLY_API_KEY" | jq '.[] | select( .login | ascii_downcase == '$USER_EMAIL')' | jq -r '"\(.id):\(.role):\(.limit_services)"' | tr -d '\n' )

if [[ "${USER_INFO}" == "" ]];
then
  service_detail=$(printf "\nUSER NOT FOUND")
else
  USER_ID_TEMP=$(echo "$USER_INFO" | awk -F':' '{print $1}')
  USER_ID=$(echo "\"$USER_ID_TEMP\"")
  USER_ROLE=$(echo "$USER_INFO" | awk -F':' '{print $2}')
  USER_LIMIT=$(echo "$USER_INFO" | awk -F':' '{print $3}')

  if [[ "$USER_ROLE" == "user" ]]; 
  then
    service_detail=$(printf "\nUSER-ROLE:{READ-ONLY}")
  elif [[ "$USER_ROLE" == "superuser" ]];
  then
    service_detail=$(printf "\nUSER-ROLE:{SUPERUSER}")
  elif [[ "$USER_ROLE" == "engineer" && "$USER_LIMIT" == "false" ]];
  then
    service_detail=$(printf "\nUSER-ROLE:{Engineer-AllAccess}")
  else
    allThreads=$(curl --location --request GET 'https://api.fastly.com/service-authorizations?page%5Bnumber%5D=1&page%5Bsize%5D=500000' --header "Fastly-Key: $FASTLY_API_KEY" | jq '.data[] | select(.relationships.user.data.id == '$USER_ID')' | jq -r '"\(.relationships.service.data.id):\(.attributes.permission)"')

    for t in ${allThreads[@]}; do
      service_id=$(echo "$t" | awk -F':' '{print $1}')
      permission=$(echo "$t" | awk -F':' '{print $2}')
      service_name=$(curl --location --request GET "https://api.fastly.com/service/{$service_id}" --header "Fastly-Key: $FASTLY_API_KEY" | jq '.name')
      service_detail+=$(printf "\nSERVICE-NAME:{$service_name}, PERMISSION{$permission"})
    done
  fi
fi

printf "\n<<<<<<<<<<<<<< User Permissions <<<<<<<<<<<<<<<<<\n"
echo "$service_detail"
printf "\n>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\n"