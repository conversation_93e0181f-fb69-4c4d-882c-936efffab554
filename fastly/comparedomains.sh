# cat production.json | jq -r '.certs[].dnsNames[]' | sort > fastcrypt/production.txt
# cat redirects.json | jq -r '.certs[].dnsNames[]' | sort > fastcrypt/redirects.txt
# cat tangent.json | jq -r '.certs[].dnsNames[]' | sort > fastcrypt/tangent.txt
# cat uw.json | jq -r '.certs[].dnsNames[]' | sort > fastcrypt/uw.txt

# files in fastcrypt/production not in fastly
comm -23 fastcrypt/production.txt domains_in-use/domains.txt
*.timeandmoney.com
timeandmoney.com

comm -23 fastcrypt/redirects.txt domains_in-use/domains.txt
*.thepeoplesentinel.com
thepeoplesentinel.com
wpad.dat

comm -23 fastcrypt/tangent.txt domains_in-use/domains.txt
*.admeter.usatoday.com
*.aggieswire.usatoday.com
*.auburnwire.usatoday.com
*.badgerswire.usatoday.com
*.bearswire.usatoday.com
*.bengalswire.usatoday.com
*.billswire.usatoday.com
*.boxingjunkie.usatoday.com
*.broncoswire.usatoday.com
*.brownswire.usatoday.com
*.buckeyeswire.usatoday.com
*.bucswire.usatoday.com
*.cardswire.usatoday.com
*.celticswire.usatoday.com
*.chargerswire.usatoday.com
*.chiefswire.usatoday.com
*.clemsonwire.usatoday.com
*.collegesportswire.usatoday.com
*.coloradobuffaloeswire.usatoday.com
*.coltswire.usatoday.com
*.commanderswire.usatoday.com
*.cornhuskerswire.usatoday.com
*.cowboyswire.usatoday.com
*.development.gannettdigital.com
*.dolphinswire.usatoday.com
*.draftwire.usatoday.com
*.duckswire.usatoday.com
*.dukewire.usatoday.com
*.fightingirishwire.usatoday.com
*.fsuwire.usatoday.com
*.ftw.usatoday.com
*.gannett-cdn.com
*.gannettdigital.com
*.gatorswire.usatoday.com
*.gcion.com
*.giantswire.usatoday.com
*.golfweek.usatoday.com
*.hawkeyeswire.usatoday.com
*.jaguarswire.usatoday.com
*.jetswire.usatoday.com
*.lebronwire.usatoday.com
*.lionswire.usatoday.com
*.longhornswire.usatoday.com
*.lsutigerswire.usatoday.com
*.mmajunkie.usatoday.com
*.motorsportswire.usatoday.com
*.moviemeter.usatoday.com
*.netswire.usatoday.com
*.ninerswire.usatoday.com
*.nittanylionswire.usatoday.com
*.okcthunderwire.usatoday.com
*.oklahoman.com
*.packerswire.usatoday.com
*.pantherswire.usatoday.com
*.patriotswire.usatoday.com
*.prosoccerwire.usatoday.com
*.raiderswire.usatoday.com
*.ravenswire.usatoday.com
*.rocketswire.usatoday.com
*.rolltidewire.usatoday.com
*.saintswire.usatoday.com
*.seahawkswire.usatoday.com
*.sixerswire.usatoday.com
*.soonerswire.usatoday.com
*.spartanswire.usatoday.com
*.sportsbookwire.usatoday.com
*.sportswire.usatoday.com
*.statesman.com
*.steelerswire.usatoday.com
*.tarheelswire.usatoday.com
*.texanswire.usatoday.com
*.theeagleswire.usatoday.com
*.thefalconswire.usatoday.com
*.thelistwire.usatoday.com
*.theramswire.usatoday.com
*.therookiewire.usatoday.com
*.titanswire.usatoday.com
*.touchdownwire.usatoday.com
*.trainusatoday.com
*.trainuscp.com
*.trojanswire.usatoday.com
*.uclawire.usatoday.com
*.ugawire.usatoday.com
*.ukwildcatswire.usatoday.com
*.usatodaynetworkservice.com
*.uwhuskieswire.usatoday.com
*.vikingswire.usatoday.com
*.volswire.usatoday.com
*.warriorswire.usatoday.com
*.wolverineswire.usatoday.com
*.wrestlingjunkie.usatoday.com

comm -23 fastcrypt/uw.txt domains_in-use/domains.txt
*.admeter.usatoday.com
*.aggieswire.usatoday.com
*.auburnwire.usatoday.com
*.badgerswire.usatoday.com
*.bearswire.usatoday.com
*.bengalswire.usatoday.com
*.billswire.usatoday.com
*.boxingjunkie.usatoday.com
*.broncoswire.usatoday.com
*.brownswire.usatoday.com
*.buckeyeswire.usatoday.com
*.bucswire.usatoday.com
*.cardswire.usatoday.com
*.celticswire.usatoday.com
*.centralfloridafuture.com
*.chargerswire.usatoday.com
*.chiefswire.usatoday.com
*.clemsonwire.usatoday.com
*.collegesportswire.usatoday.com
*.coloradobuffaloeswire.usatoday.com
*.coltswire.usatoday.com
*.commanderswire.usatoday.com
*.cornhuskerswire.usatoday.com
*.cowboyswire.usatoday.com
*.development.gannettdigital.com
*.dolphinswire.usatoday.com
*.draftwire.usatoday.com
*.duckswire.usatoday.com
*.dukewire.usatoday.com
*.fightingirishwire.usatoday.com
*.fsuwire.usatoday.com
*.ftw.usatoday.com
*.gatorswire.usatoday.com
*.giantswire.usatoday.com
*.golfweek.usatoday.com
*.hawkeyeswire.usatoday.com
*.jaguarswire.usatoday.com
*.jetswire.usatoday.com
*.lebronwire.usatoday.com
*.lionswire.usatoday.com
*.longhornswire.usatoday.com
*.lsutigerswire.usatoday.com
*.mmajunkie.usatoday.com
*.motorsportswire.usatoday.com
*.moviemeter.usatoday.com
*.netswire.usatoday.com
*.ninerswire.usatoday.com
*.nittanylionswire.usatoday.com
*.okcthunderwire.usatoday.com
*.packerswire.usatoday.com
*.pantherswire.usatoday.com
*.patriotswire.usatoday.com
*.prosoccerwire.usatoday.com
*.raiderswire.usatoday.com
*.ravenswire.usatoday.com
*.rocketswire.usatoday.com
*.rolltidewire.usatoday.com
*.saintswire.usatoday.com
*.seahawkswire.usatoday.com
*.sekvoice.com
*.sixerswire.usatoday.com
*.soonerswire.usatoday.com
*.spartanswire.usatoday.com
*.sportsbookwire.usatoday.com
*.sportswire.usatoday.com
*.steelerswire.usatoday.com
*.tarheelswire.usatoday.com
*.texanswire.usatoday.com
*.theeagleswire.usatoday.com
*.thefalconswire.usatoday.com
*.thelistwire.usatoday.com
*.theramswire.usatoday.com
*.therookiewire.usatoday.com
*.timeandmoney.com
*.titanswire.usatoday.com
*.touchdownwire.usatoday.com
*.trojanswire.usatoday.com
*.uclawire.usatoday.com
*.ugawire.usatoday.com
*.ukwildcatswire.usatoday.com
*.usatoday.com
*.uwhuskieswire.usatoday.com
*.vikingswire.usatoday.com
*.volswire.usatoday.com
*.warriorswire.usatoday.com
*.wolverineswire.usatoday.com
*.wrestlingjunkie.usatoday.com
