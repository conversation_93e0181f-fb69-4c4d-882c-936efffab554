Creating NS1 Record: cm-stage.aberdeennews.com
::error::Failed to create record: cm-stage.aberdeennews.com
{"message":"record already exists","details":[{"type":"request-id","message":"fe7df4b5-1596-4743-8e74-61a5b46ddf64"}]}
Creating NS1 Record: cm-stage.adelnews.com
::error::Failed to create record: cm-stage.adelnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"e7e3c6d3-db6a-4974-b35b-bd07271b2589"}]}
Creating NS1 Record: cm-stage.aledotimesrecord.com
::error::Failed to create record: cm-stage.aledotimesrecord.com
{"message":"record already exists","details":[{"type":"request-id","message":"466c26f8-a107-4c45-9e96-7d4f395b418e"}]}
Creating NS1 Record: cm-stage.amarillo.com
::error::Failed to create record: cm-stage.amarillo.com
{"message":"record already exists","details":[{"type":"request-id","message":"02c64bea-0f7f-445a-9a7f-267408883f41"}]}
Creating NS1 Record: cm-stage.amestrib.com
::error::Failed to create record: cm-stage.amestrib.com
{"message":"record already exists","details":[{"type":"request-id","message":"1583b465-fdfa-4929-b5b0-a41c3562c192"}]}
Creating NS1 Record: cm-stage.app.com
Successfully created record for cm-stage.app.com
Creating NS1 Record: cm-stage.argusleader.com
Successfully created record for cm-stage.argusleader.com
Creating NS1 Record: cm-stage.augustachronicle.com
::error::Failed to create record: cm-stage.augustachronicle.com
{"message":"record already exists","details":[{"type":"request-id","message":"a6eeab9d-1735-430a-94dc-8bf08af0a2ff"}]}
Creating NS1 Record: cm-stage.austin360.com
::error::Failed to create record: cm-stage.austin360.com
{"message":"record already exists","details":[{"type":"request-id","message":"ab287e71-89ad-4efc-85fd-443ccab3d57f"}]}
Creating NS1 Record: cm-stage.azcentral.com
Successfully created record for cm-stage.azcentral.com
Creating NS1 Record: cm-stage.barnesville-enterprise.com
::error::Failed to create record: cm-stage.barnesville-enterprise.com
{"message":"record already exists","details":[{"type":"request-id","message":"7282b1a0-9ed4-48ad-8888-28a159861806"}]}
Creating NS1 Record: cm-stage.barnstablepatriot.com
::error::Failed to create record: cm-stage.barnstablepatriot.com
{"message":"record already exists","details":[{"type":"request-id","message":"e038dfcd-cd71-47ea-ba72-b7317682a2d5"}]}
Creating NS1 Record: cm-stage.battlecreekenquirer.com
Successfully created record for cm-stage.battlecreekenquirer.com
Creating NS1 Record: cm-stage.baystateparent.com
::error::Failed to create record: cm-stage.baystateparent.com
{"message":"record already exists","details":[{"type":"request-id","message":"1cc965f8-5681-408c-a09e-255ce8c4853f"}]}
Creating NS1 Record: cm-stage.beaconjournal.com
::error::Failed to create record: cm-stage.beaconjournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"8729df7f-d60c-4e04-81e3-d0ab61b32073"}]}
Creating NS1 Record: cm-stage.blackmountainnews.com
Successfully created record for cm-stage.blackmountainnews.com
Creating NS1 Record: cm-stage.blueridgenow.com
::error::Failed to create record: cm-stage.blueridgenow.com
{"message":"record already exists","details":[{"type":"request-id","message":"1d2ee9ed-40a9-496a-8c5b-9bf0551a9a7d"}]}
Creating NS1 Record: cm-stage.blufftontoday.com
::error::Failed to create record: cm-stage.blufftontoday.com
{"message":"record already exists","details":[{"type":"request-id","message":"7881a19d-e55f-4537-aa65-80710c97b9e6"}]}
Creating NS1 Record: cm-stage.boonevilledemocrat.com
::error::Failed to create record: cm-stage.boonevilledemocrat.com
{"message":"record already exists","details":[{"type":"request-id","message":"40164381-60d7-46e5-8e89-296f9f6fc9f0"}]}
Creating NS1 Record: cm-stage.buckeyextra.com
::error::Failed to create record: cm-stage.buckeyextra.com
{"message":"record already exists","details":[{"type":"request-id","message":"0a7340a8-2ff5-4d9e-8164-535c8c7f3b86"}]}
Creating NS1 Record: cm-stage.buckscountycouriertimes.com
::error::Failed to create record: cm-stage.buckscountycouriertimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"d339eb33-b5e5-43aa-ab91-d5e24ab6e7d4"}]}
Creating NS1 Record: cm-stage.bucyrustelegraphforum.com
Successfully created record for cm-stage.bucyrustelegraphforum.com
Creating NS1 Record: cm-stage.burlingtoncountytimes.com
::error::Failed to create record: cm-stage.burlingtoncountytimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"07d8e231-1560-453f-bee2-2338dfa66265"}]}
Creating NS1 Record: cm-stage.burlingtonfreepress.com
::error::Failed to create record: cm-stage.burlingtonfreepress.com
{"message":"record already exists","details":[{"type":"request-id","message":"810867a9-08c5-4c4a-8dd7-d5bf5e2975de"}]}
Creating NS1 Record: cm-stage.caller.com
Successfully created record for cm-stage.caller.com
Creating NS1 Record: cm-stage.cantondailyledger.com
::error::Failed to create record: cm-stage.cantondailyledger.com
{"message":"record already exists","details":[{"type":"request-id","message":"a9f1b92d-8150-495c-a5cb-d66574efd8fe"}]}
Creating NS1 Record: cm-stage.cantonrep.com
::error::Failed to create record: cm-stage.cantonrep.com
{"message":"record already exists","details":[{"type":"request-id","message":"a0ceff50-18b2-450d-9bdc-db71e3d1e9a1"}]}
Creating NS1 Record: cm-stage.capecodtimes.com
::error::Failed to create record: cm-stage.capecodtimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"07465935-bdc2-4593-9d91-b06a1e0e83d3"}]}
Creating NS1 Record: cm-stage.charlestonexpress.com
::error::Failed to create record: cm-stage.charlestonexpress.com
{"message":"record already exists","details":[{"type":"request-id","message":"f8c5a160-71e6-452c-a5ff-7c05b2624407"}]}
Creating NS1 Record: cm-stage.cheboygannews.com
::error::Failed to create record: cm-stage.cheboygannews.com
{"message":"record already exists","details":[{"type":"request-id","message":"4f8390f2-3a07-4cd3-a1bf-d811a6292c0b"}]}
Creating NS1 Record: cm-stage.chieftain.com
::error::Failed to create record: cm-stage.chieftain.com
{"message":"record already exists","details":[{"type":"request-id","message":"4ba8d1c0-29f8-4978-bd6a-81be192e701c"}]}
Creating NS1 Record: cm-stage.chillicothegazette.com
Successfully created record for cm-stage.chillicothegazette.com
Creating NS1 Record: cm-stage.chillicothetimesbulletin.com
::error::Failed to create record: cm-stage.chillicothetimesbulletin.com
{"message":"record already exists","details":[{"type":"request-id","message":"917e1a85-6550-454a-8fa0-0359451e9c97"}]}
Creating NS1 Record: cm-stage.cincinnati.com
::error::Failed to create record: cm-stage.cincinnati.com
{"message":"record already exists","details":[{"type":"request-id","message":"1aac9641-8dad-47b1-92ce-45f30e4e8b69"}]}
Creating NS1 Record: cm-stage.citizen-times.com
Successfully created record for cm-stage.citizen-times.com
Creating NS1 Record: cm-stage.cjonline.com
::error::Failed to create record: cm-stage.cjonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"19867940-9dbb-46d8-bb6d-e733a501036b"}]}
Creating NS1 Record: cm-stage.clarionledger.com
Successfully created record for cm-stage.clarionledger.com
Creating NS1 Record: cm-stage.coloradoan.com
Successfully created record for cm-stage.coloradoan.com
Creating NS1 Record: cm-stage.columbiadailyherald.com
::error::Failed to create record: cm-stage.columbiadailyherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"ff45db19-4cc8-45c4-b8f1-57bb7d632998"}]}
Creating NS1 Record: cm-stage.columbiatribune.com
::error::Failed to create record: cm-stage.columbiatribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"4b0751fb-2bea-4ef6-a35c-7793f5c07abb"}]}
Creating NS1 Record: cm-stage.columbusalive.com
::error::Failed to create record: cm-stage.columbusalive.com
{"message":"record already exists","details":[{"type":"request-id","message":"63440f70-f540-49a8-a407-259947673df3"}]}
Creating NS1 Record: cm-stage.columbusceo.com
::error::Failed to create record: cm-stage.columbusceo.com
{"message":"record already exists","details":[{"type":"request-id","message":"0dd6af5c-d3b8-4421-a438-4b50cbe1779c"}]}
Creating NS1 Record: cm-stage.columbusmonthly.com
::error::Failed to create record: cm-stage.columbusmonthly.com
{"message":"record already exists","details":[{"type":"request-id","message":"0fcd663e-78eb-4eaa-97b0-c8860d1b1089"}]}
Creating NS1 Record: cm-stage.columbusparent.com
::error::Failed to create record: cm-stage.columbusparent.com
{"message":"record already exists","details":[{"type":"request-id","message":"404ef846-bd80-4620-8a98-59a3d81afbe0"}]}
Creating NS1 Record: cm-stage.commercialappeal.com
Successfully created record for cm-stage.commercialappeal.com
Creating NS1 Record: cm-stage.coshoctontribune.com
Successfully created record for cm-stage.coshoctontribune.com
Creating NS1 Record: cm-stage.courier-journal.com
Successfully created record for cm-stage.courier-journal.com
Creating NS1 Record: cm-stage.courierpostonline.com
Successfully created record for cm-stage.courierpostonline.com
Creating NS1 Record: cm-stage.courierpress.com
Successfully created record for cm-stage.courierpress.com
Creating NS1 Record: cm-stage.daily-jeff.com
::error::Failed to create record: cm-stage.daily-jeff.com
{"message":"record already exists","details":[{"type":"request-id","message":"9d3f4827-de4d-4ddc-9b55-2d24ca0f95ba"}]}
Creating NS1 Record: cm-stage.dailyamerican.com
::error::Failed to create record: cm-stage.dailyamerican.com
{"message":"record already exists","details":[{"type":"request-id","message":"2464bb94-e268-43d9-b0b4-da40bb272494"}]}
Creating NS1 Record: cm-stage.dailycomet.com
::error::Failed to create record: cm-stage.dailycomet.com
{"message":"record already exists","details":[{"type":"request-id","message":"52bcf11a-66fc-41a6-80e8-7872a1bff80e"}]}
Creating NS1 Record: cm-stage.dailycommercial.com
::error::Failed to create record: cm-stage.dailycommercial.com
{"message":"record already exists","details":[{"type":"request-id","message":"ce2f7898-058f-434a-8684-0d45cfa51878"}]}
Creating NS1 Record: cm-stage.dailyrecord.com
Successfully created record for cm-stage.dailyrecord.com
Creating NS1 Record: cm-stage.dailyworld.com
Successfully created record for cm-stage.dailyworld.com
Creating NS1 Record: cm-stage.dansvilleonline.com
::error::Failed to create record: cm-stage.dansvilleonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"82903972-2117-42e8-95e6-62350cb72d0f"}]}
Creating NS1 Record: cm-stage.delawareonline.com
Successfully created record for cm-stage.delawareonline.com
Creating NS1 Record: cm-stage.delmarvanow.com
Successfully created record for cm-stage.delmarvanow.com
Creating NS1 Record: cm-stage.democratandchronicle.com
::error::Failed to create record: cm-stage.democratandchronicle.com
{"message":"record already exists","details":[{"type":"request-id","message":"e39eecf8-6298-4c29-af89-dc30ab4f5f39"}]}
Creating NS1 Record: cm-stage.desertsun.com
::error::Failed to create record: cm-stage.desertsun.com
{"message":"record already exists","details":[{"type":"request-id","message":"13a8157f-7303-49c7-a793-1a97e989ad1f"}]}
Creating NS1 Record: cm-stage.desmoinesregister.com
Successfully created record for cm-stage.desmoinesregister.com
Creating NS1 Record: cm-stage.detroitnews.com
::error::Failed to create record: cm-stage.detroitnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"fdf05986-a634-4187-bfe5-62196789a65f"}]}
Creating NS1 Record: cm-stage.dispatch.com
::error::Failed to create record: cm-stage.dispatch.com
{"message":"record already exists","details":[{"type":"request-id","message":"850f5bcb-8ffd-4ae6-bafb-4da432d08f41"}]}
Creating NS1 Record: cm-stage.dmjuice.com
Successfully created record for cm-stage.dmjuice.com
Creating NS1 Record: cm-stage.dnj.com
Successfully created record for cm-stage.dnj.com
Creating NS1 Record: cm-stage.donaldsonvillechief.com
::error::Failed to create record: cm-stage.donaldsonvillechief.com
{"message":"record already exists","details":[{"type":"request-id","message":"f640af1b-1b22-4025-b074-ddcbe0082b81"}]}
Creating NS1 Record: cm-stage.doverpost.com
::error::Failed to create record: cm-stage.doverpost.com
{"message":"record already exists","details":[{"type":"request-id","message":"fb3421ec-280a-4178-b520-9d8ded2014e5"}]}
Creating NS1 Record: cm-stage.eastpeoriatimescourier.com
::error::Failed to create record: cm-stage.eastpeoriatimescourier.com
{"message":"record already exists","details":[{"type":"request-id","message":"2e44f78a-1319-4bb6-8e6e-bb0cc701d9c0"}]}
Creating NS1 Record: cm-stage.echo-pilot.com
::error::Failed to create record: cm-stage.echo-pilot.com
{"message":"record already exists","details":[{"type":"request-id","message":"382c2a54-4c90-4158-bb77-539a1b7ad89e"}]}
Creating NS1 Record: cm-stage.ellwoodcityledger.com
::error::Failed to create record: cm-stage.ellwoodcityledger.com
{"message":"record already exists","details":[{"type":"request-id","message":"b6831ae3-0667-4fa7-bd44-307fba43fc50"}]}
Creating NS1 Record: cm-stage.elpasotimes.com
Successfully created record for cm-stage.elpasotimes.com
Creating NS1 Record: cm-stage.elpasoymas.com
Successfully created record for cm-stage.elpasoymas.com
Creating NS1 Record: cm-stage.elsoldesalinas.com
Successfully created record for cm-stage.elsoldesalinas.com
Creating NS1 Record: cm-stage.enterprisenews.com
::error::Failed to create record: cm-stage.enterprisenews.com
{"message":"record already exists","details":[{"type":"request-id","message":"129ab93e-5a72-49bd-bc48-368ed70382dc"}]}
Creating NS1 Record: cm-stage.eveningsun.com
Successfully created record for cm-stage.eveningsun.com
Creating NS1 Record: cm-stage.eveningtribune.com
::error::Failed to create record: cm-stage.eveningtribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"af87d224-41d4-4b56-879f-68b83f8d69ce"}]}
Creating NS1 Record: cm-stage.examiner-enterprise.com
::error::Failed to create record: cm-stage.examiner-enterprise.com
{"message":"record already exists","details":[{"type":"request-id","message":"d0461a1b-6d33-4ccb-99a4-765ba9f90703"}]}
Creating NS1 Record: cm-stage.farmersadvance.com
Successfully created record for cm-stage.farmersadvance.com
Creating NS1 Record: cm-stage.farmforum.net
::error::Failed to create record: cm-stage.farmforum.net
{"message":"record already exists","details":[{"type":"request-id","message":"c7bf5ed8-98ad-4f7e-9c98-d289a86c0c86"}]}
Creating NS1 Record: cm-stage.fayobserver.com
::error::Failed to create record: cm-stage.fayobserver.com
{"message":"record already exists","details":[{"type":"request-id","message":"0196ce06-2ef5-45fb-8060-25932ac8dde6"}]}
Creating NS1 Record: cm-stage.fdlreporter.com
Successfully created record for cm-stage.fdlreporter.com
Creating NS1 Record: cm-stage.flipsidepa.com
Successfully created record for cm-stage.flipsidepa.com
Creating NS1 Record: cm-stage.floridatoday.com
Successfully created record for cm-stage.floridatoday.com
Creating NS1 Record: cm-stage.fosters.com
::error::Failed to create record: cm-stage.fosters.com
{"message":"record already exists","details":[{"type":"request-id","message":"db32e4a9-83f2-42b3-8f7e-9a1402641d0e"}]}
Creating NS1 Record: cm-stage.freep.com
::error::Failed to create record: cm-stage.freep.com
{"message":"record already exists","details":[{"type":"request-id","message":"edaa2cef-df8a-4436-8eed-b1e043b75ff6"}]}
Creating NS1 Record: cm-stage.fsunews.com
Successfully created record for cm-stage.fsunews.com
Creating NS1 Record: cm-stage.gadsdentimes.com
::error::Failed to create record: cm-stage.gadsdentimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"c867f03e-8d15-44be-81b7-b2f873570caa"}]}
Creating NS1 Record: cm-stage.gainesville.com
::error::Failed to create record: cm-stage.gainesville.com
{"message":"record already exists","details":[{"type":"request-id","message":"a7262dd4-ee2d-498a-aa19-a60872a4a3ad"}]}
Creating NS1 Record: cm-stage.galesburg.com
::error::Failed to create record: cm-stage.galesburg.com
{"message":"record already exists","details":[{"type":"request-id","message":"26351f66-6bd5-4067-be8c-a3127d959394"}]}
Creating NS1 Record: cm-stage.gametimepa.com
Successfully created record for cm-stage.gametimepa.com
Creating NS1 Record: cm-stage.gannett.com
Successfully created record for cm-stage.gannett.com
Creating NS1 Record: cm-stage.gastongazette.com
::error::Failed to create record: cm-stage.gastongazette.com
{"message":"record already exists","details":[{"type":"request-id","message":"6d1303f9-ef18-4c9e-8aa5-e7d0b0499845"}]}
Creating NS1 Record: cm-stage.gatorsports.com
::error::Failed to create record: cm-stage.gatorsports.com
{"message":"record already exists","details":[{"type":"request-id","message":"90aa6fd8-4dfb-4f4b-bc18-c950fbbd36a4"}]}
Creating NS1 Record: cm-stage.geneseorepublic.com
::error::Failed to create record: cm-stage.geneseorepublic.com
{"message":"record already exists","details":[{"type":"request-id","message":"cc6538f9-5043-44c0-a1bb-7e94437c61ca"}]}
Creating NS1 Record: cm-stage.goerie.com
::error::Failed to create record: cm-stage.goerie.com
{"message":"record already exists","details":[{"type":"request-id","message":"3d85d795-f2fb-442e-bea2-9584296d5978"}]}
Creating NS1 Record: cm-stage.gosanangelo.com
Successfully created record for cm-stage.gosanangelo.com
Creating NS1 Record: cm-stage.goupstate.com
::error::Failed to create record: cm-stage.goupstate.com
{"message":"record already exists","details":[{"type":"request-id","message":"8edc015f-873f-4306-a15a-a71c5b568e2e"}]}
Creating NS1 Record: cm-stage.greatfallstribune.com
Successfully created record for cm-stage.greatfallstribune.com
Creating NS1 Record: cm-stage.greenbaypressgazette.com
Successfully created record for cm-stage.greenbaypressgazette.com
Creating NS1 Record: cm-stage.greenvilleonline.com
Successfully created record for cm-stage.greenvilleonline.com
Creating NS1 Record: cm-stage.hattiesburgamerican.com
Successfully created record for cm-stage.hattiesburgamerican.com
Creating NS1 Record: cm-stage.hawkcentral.com
Successfully created record for cm-stage.hawkcentral.com
Creating NS1 Record: cm-stage.heraldmailmedia.com
::error::Failed to create record: cm-stage.heraldmailmedia.com
{"message":"record already exists","details":[{"type":"request-id","message":"4cedb464-1573-45be-bd3c-4da9ea7f5a9e"}]}
Creating NS1 Record: cm-stage.heraldnews.com
::error::Failed to create record: cm-stage.heraldnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"a8641d99-aafa-48a7-a1ef-953d69b745ab"}]}
Creating NS1 Record: cm-stage.heraldtimesonline.com
::error::Failed to create record: cm-stage.heraldtimesonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"b0f2d5d6-f60f-4f29-8150-dc5c33de4f55"}]}
Creating NS1 Record: cm-stage.heraldtribune.com
::error::Failed to create record: cm-stage.heraldtribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"76de6564-5b35-43d6-975d-cf6fa20734c8"}]}
Creating NS1 Record: cm-stage.hillsdale.net
::error::Failed to create record: cm-stage.hillsdale.net
{"message":"record already exists","details":[{"type":"request-id","message":"49ca69dc-3976-43d3-b3fc-564878b5d763"}]}
Creating NS1 Record: cm-stage.hockessincommunitynews.com
::error::Failed to create record: cm-stage.hockessincommunitynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"44df2d76-f05d-46bd-a412-cd55e65f3cd5"}]}
Creating NS1 Record: cm-stage.hollandsentinel.com
::error::Failed to create record: cm-stage.hollandsentinel.com
{"message":"record already exists","details":[{"type":"request-id","message":"c1dcb194-d95a-4858-99f7-537970a3a969"}]}
Creating NS1 Record: cm-stage.hometownlife.com
Successfully created record for cm-stage.hometownlife.com
Creating NS1 Record: cm-stage.hookem.com
::error::Failed to create record: cm-stage.hookem.com
{"message":"record already exists","details":[{"type":"request-id","message":"51037e1b-d18f-447d-8b71-3097fad69ef1"}]}
Creating NS1 Record: cm-stage.hoopshype.com
Successfully created record for cm-stage.hoopshype.com
Creating NS1 Record: cm-stage.houmatoday.com
::error::Failed to create record: cm-stage.houmatoday.com
{"message":"record already exists","details":[{"type":"request-id","message":"8656f2a1-ca2d-4bbf-8ae2-9a364707cd70"}]}
Creating NS1 Record: cm-stage.htrnews.com
Successfully created record for cm-stage.htrnews.com
Creating NS1 Record: cm-stage.hutchnews.com
::error::Failed to create record: cm-stage.hutchnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"9e6239b2-3666-4447-9d24-61e01330517a"}]}
Creating NS1 Record: cm-stage.indeonline.com
::error::Failed to create record: cm-stage.indeonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"fd592f9b-0aea-466e-a26d-3b7b68c41b8a"}]}
Creating NS1 Record: cm-stage.independentmail.com
::error::Failed to create record: cm-stage.independentmail.com
{"message":"record already exists","details":[{"type":"request-id","message":"4f14760a-3524-47e9-a7c2-3bf6d5fd4370"}]}
Creating NS1 Record: cm-stage.indystar.com
::error::Failed to create record: cm-stage.indystar.com
{"message":"record already exists","details":[{"type":"request-id","message":"cc455482-928d-4bc1-b419-c6ecc53e5c9f"}]}
Creating NS1 Record: cm-stage.inyork.com
Successfully created record for cm-stage.inyork.com
Creating NS1 Record: cm-stage.ithacajournal.com
Successfully created record for cm-stage.ithacajournal.com
Creating NS1 Record: cm-stage.jacksonsun.com
Successfully created record for cm-stage.jacksonsun.com
Creating NS1 Record: cm-stage.jacksonville.com
::error::Failed to create record: cm-stage.jacksonville.com
{"message":"record already exists","details":[{"type":"request-id","message":"e005187a-a50c-4168-83ac-d3bced1a48c6"}]}
Creating NS1 Record: cm-stage.jconline.com
Successfully created record for cm-stage.jconline.com
Creating NS1 Record: cm-stage.jdnews.com
::error::Failed to create record: cm-stage.jdnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"ec2d015a-ae4f-4a16-a919-c348fcfecdbd"}]}
Creating NS1 Record: cm-stage.journalstandard.com
::error::Failed to create record: cm-stage.journalstandard.com
{"message":"record already exists","details":[{"type":"request-id","message":"f0e5adc7-ad9d-4a8d-9766-b21b0dd78bee"}]}
Creating NS1 Record: cm-stage.jsonline.com
::error::Failed to create record: cm-stage.jsonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"5ca83abb-3fe8-490a-a41a-67d2b6e6676e"}]}
Creating NS1 Record: cm-stage.kitsapsun.com
Successfully created record for cm-stage.kitsapsun.com
Creating NS1 Record: cm-stage.knoxnews.com
Successfully created record for cm-stage.knoxnews.com
Creating NS1 Record: cm-stage.lancastereaglegazette.com
Successfully created record for cm-stage.lancastereaglegazette.com
Creating NS1 Record: cm-stage.lansingstatejournal.com
Successfully created record for cm-stage.lansingstatejournal.com
Creating NS1 Record: cm-stage.lavozarizona.com
Successfully created record for cm-stage.lavozarizona.com
Creating NS1 Record: cm-stage.lcsun-news.com
Successfully created record for cm-stage.lcsun-news.com
Creating NS1 Record: cm-stage.ldnews.com
Successfully created record for cm-stage.ldnews.com
Creating NS1 Record: cm-stage.lenconnect.com
::error::Failed to create record: cm-stage.lenconnect.com
{"message":"record already exists","details":[{"type":"request-id","message":"e55e9e8c-c1c5-4b5a-a0dd-b2fb539b9bc8"}]}
Creating NS1 Record: cm-stage.lincolncourier.com
::error::Failed to create record: cm-stage.lincolncourier.com
{"message":"record already exists","details":[{"type":"request-id","message":"28c1bafd-7564-4bc7-8ed5-442923a2d77e"}]}
Creating NS1 Record: cm-stage.linkbostonhomes.com
::error::Failed to create record: cm-stage.linkbostonhomes.com
{"message":"record already exists","details":[{"type":"request-id","message":"3d59cabf-bc19-422f-8e40-fca0ecebfb55"}]}
Creating NS1 Record: cm-stage.livingstondaily.com
Successfully created record for cm-stage.livingstondaily.com
Creating NS1 Record: cm-stage.lohud.com
Successfully created record for cm-stage.lohud.com
Creating NS1 Record: cm-stage.lubbockonline.com
::error::Failed to create record: cm-stage.lubbockonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"9e8e0634-9e4d-46cf-b03e-1fdda6c91440"}]}
Creating NS1 Record: cm-stage.mansfieldnewsjournal.com
Successfully created record for cm-stage.mansfieldnewsjournal.com
Creating NS1 Record: cm-stage.marconews.com
Successfully created record for cm-stage.marconews.com
Creating NS1 Record: cm-stage.marionstar.com
Successfully created record for cm-stage.marionstar.com
Creating NS1 Record: cm-stage.marshfieldnewsherald.com
Successfully created record for cm-stage.marshfieldnewsherald.com
Creating NS1 Record: cm-stage.mcdonoughvoice.com
::error::Failed to create record: cm-stage.mcdonoughvoice.com
{"message":"record already exists","details":[{"type":"request-id","message":"e7e81d9d-f922-4133-97bd-9579f38b5033"}]}
Creating NS1 Record: cm-stage.metrowestdailynews.com
::error::Failed to create record: cm-stage.metrowestdailynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"3c4fdf34-a62d-49b4-a88a-7decaee9fa1f"}]}
Creating NS1 Record: cm-stage.middletowntranscript.com
::error::Failed to create record: cm-stage.middletowntranscript.com
{"message":"record already exists","details":[{"type":"request-id","message":"eb2cf5a6-16db-4a5f-80e1-b4b9e80cdfac"}]}
Creating NS1 Record: cm-stage.milfordbeacon.com
::error::Failed to create record: cm-stage.milfordbeacon.com
{"message":"record already exists","details":[{"type":"request-id","message":"74b815f2-87c1-4630-b7c0-3caee9de54df"}]}
Creating NS1 Record: cm-stage.milforddailynews.com
::error::Failed to create record: cm-stage.milforddailynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"abb47308-ddd2-45e7-b0d6-c159d00f31e4"}]}
Creating NS1 Record: cm-stage.monroecopost.com
::error::Failed to create record: cm-stage.monroecopost.com
{"message":"record already exists","details":[{"type":"request-id","message":"bd848c49-0537-4479-9ddb-87e8a10a5296"}]}
Creating NS1 Record: cm-stage.monroenews.com
::error::Failed to create record: cm-stage.monroenews.com
{"message":"record already exists","details":[{"type":"request-id","message":"93ecf520-97e0-9b9b-ad27-2d56e6c789f8"}]}
Creating NS1 Record: cm-stage.montgomeryadvertiser.com
Successfully created record for cm-stage.montgomeryadvertiser.com
Creating NS1 Record: cm-stage.mortontimesnews.com
::error::Failed to create record: cm-stage.mortontimesnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"dab6b43b-cb45-47ef-bc66-2e2e2665904c"}]}
Creating NS1 Record: cm-stage.mpnnow.com
::error::Failed to create record: cm-stage.mpnnow.com
{"message":"record already exists","details":[{"type":"request-id","message":"ba0fae26-2a4d-4ab6-8145-54422a5b9481"}]}
Creating NS1 Record: cm-stage.mtshastanews.com
::error::Failed to create record: cm-stage.mtshastanews.com
{"message":"record already exists","details":[{"type":"request-id","message":"2d4b5e6b-bacd-48b8-9285-ce229f018fbd"}]}
Creating NS1 Record: cm-stage.mycentraljersey.com
Successfully created record for cm-stage.mycentraljersey.com
Creating NS1 Record: cm-stage.mytownneo.com
::error::Failed to create record: cm-stage.mytownneo.com
{"message":"record already exists","details":[{"type":"request-id","message":"d048c21b-7e64-4d68-bca1-224350482b24"}]}
Creating NS1 Record: cm-stage.naplesnews.com
Successfully created record for cm-stage.naplesnews.com
Creating NS1 Record: cm-stage.ndinsider.com
::error::Failed to create record: cm-stage.ndinsider.com
{"message":"record already exists","details":[{"type":"request-id","message":"805e2a55-5ceb-47fe-967d-795bee5c008b"}]}
Creating NS1 Record: cm-stage.nevadaiowajournal.com
::error::Failed to create record: cm-stage.nevadaiowajournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"b5f1f34b-f257-4142-8f18-2c633d18daaf"}]}
Creating NS1 Record: cm-stage.newarkadvocate.com
Successfully created record for cm-stage.newarkadvocate.com
Creating NS1 Record: cm-stage.newportri.com
::error::Failed to create record: cm-stage.newportri.com
{"message":"record already exists","details":[{"type":"request-id","message":"57b82dee-d045-4ed3-836e-c05001f6ec2c"}]}
Creating NS1 Record: cm-stage.news-journalonline.com
::error::Failed to create record: cm-stage.news-journalonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"0df5e68e-24d9-4408-914f-a3b67f92da0a"}]}
Creating NS1 Record: cm-stage.news-leader.com
Successfully created record for cm-stage.news-leader.com
Creating NS1 Record: cm-stage.news-press.com
Successfully created record for cm-stage.news-press.com
Creating NS1 Record: cm-stage.newschief.com
::error::Failed to create record: cm-stage.newschief.com
{"message":"record already exists","details":[{"type":"request-id","message":"222944e0-5f86-4f82-aebc-03daac9e6064"}]}
Creating NS1 Record: cm-stage.newsherald.com
::error::Failed to create record: cm-stage.newsherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"74edfde9-f99d-485b-a314-2a5eb91dff07"}]}
Creating NS1 Record: cm-stage.newsleader.com
Successfully created record for cm-stage.newsleader.com
Creating NS1 Record: cm-stage.newsrepublican.com
::error::Failed to create record: cm-stage.newsrepublican.com
{"message":"record already exists","details":[{"type":"request-id","message":"e7de0c4b-efb6-4303-b897-de0631dd3d25"}]}
Creating NS1 Record: cm-stage.njherald.com
::error::Failed to create record: cm-stage.njherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"5ae7febf-c488-40a7-a83f-67c5925af5a8"}]}
Creating NS1 Record: cm-stage.northjersey.com
Successfully created record for cm-stage.northjersey.com
Creating NS1 Record: cm-stage.norwichbulletin.com
::error::Failed to create record: cm-stage.norwichbulletin.com
{"message":"record already exists","details":[{"type":"request-id","message":"6940ee6e-1384-4549-b0b4-d33ec7689f97"}]}
Creating NS1 Record: cm-stage.nwfdailynews.com
::error::Failed to create record: cm-stage.nwfdailynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"f4dbf7a2-8d4d-407e-92b7-05afd0625531"}]}
Creating NS1 Record: cm-stage.oakridger.com
::error::Failed to create record: cm-stage.oakridger.com
{"message":"record already exists","details":[{"type":"request-id","message":"00916262-7709-4502-8af9-5f6c45de510c"}]}
Creating NS1 Record: cm-stage.ocala.com
::error::Failed to create record: cm-stage.ocala.com
{"message":"record already exists","details":[{"type":"request-id","message":"92806397-25e5-4ac9-922b-376f56649a24"}]}
Creating NS1 Record: cm-stage.oklahoman.com
::error::Failed to create record: cm-stage.oklahoman.com
{"message":"record already exists","details":[{"type":"request-id","message":"5ef376bc-e77e-4809-bc9c-44cc9943d8a8"}]}
Creating NS1 Record: cm-stage.onlineathens.com
::error::Failed to create record: cm-stage.onlineathens.com
{"message":"record already exists","details":[{"type":"request-id","message":"9cfcd7f4-0736-468a-81b7-2e67aa5dd524"}]}
Creating NS1 Record: cm-stage.packersnews.com
Successfully created record for cm-stage.packersnews.com
Creating NS1 Record: cm-stage.pal-item.com
Successfully created record for cm-stage.pal-item.com
Creating NS1 Record: cm-stage.palmbeachdailynews.com
::error::Failed to create record: cm-stage.palmbeachdailynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"7d7494ce-8d32-4624-b1e6-ccf0868c2ad1"}]}
Creating NS1 Record: cm-stage.palmbeachpost.com
::error::Failed to create record: cm-stage.palmbeachpost.com
{"message":"record already exists","details":[{"type":"request-id","message":"f358101e-2aa5-4316-9302-21da6741f28b"}]}
Creating NS1 Record: cm-stage.paris-express.com
::error::Failed to create record: cm-stage.paris-express.com
{"message":"record already exists","details":[{"type":"request-id","message":"b6e681bd-2c0f-4c31-968f-3ef89aa58e42"}]}
Creating NS1 Record: cm-stage.patriotledger.com
::error::Failed to create record: cm-stage.patriotledger.com
{"message":"record already exists","details":[{"type":"request-id","message":"51ad591a-263f-4b51-9069-069515aee535"}]}
Creating NS1 Record: cm-stage.pawhuskajournalcapital.com
::error::Failed to create record: cm-stage.pawhuskajournalcapital.com
{"message":"record already exists","details":[{"type":"request-id","message":"bd9dc0b2-6fe3-4ee7-a1ed-658db5aab931"}]}
Creating NS1 Record: cm-stage.pekintimes.com
::error::Failed to create record: cm-stage.pekintimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"582ebd53-b4cc-4c94-89ce-9632f0b518a1"}]}
Creating NS1 Record: cm-stage.petoskeynews.com
::error::Failed to create record: cm-stage.petoskeynews.com
{"message":"record already exists","details":[{"type":"request-id","message":"849998fd-f145-4b6e-bfba-3084c78ee680"}]}
Creating NS1 Record: cm-stage.phillyburbs.com
Successfully created record for cm-stage.phillyburbs.com
Creating NS1 Record: cm-stage.pjstar.com
::error::Failed to create record: cm-stage.pjstar.com
{"message":"record already exists","details":[{"type":"request-id","message":"85e11b32-16b0-44c9-bf07-58516605fd83"}]}
Creating NS1 Record: cm-stage.pnj.com
Successfully created record for cm-stage.pnj.com
Creating NS1 Record: cm-stage.poconorecord.com
::error::Failed to create record: cm-stage.poconorecord.com
{"message":"record already exists","details":[{"type":"request-id","message":"61e29d9a-a17a-4b8e-9a10-f7f0d5de04ef"}]}
Creating NS1 Record: cm-stage.pontiacdailyleader.com
::error::Failed to create record: cm-stage.pontiacdailyleader.com
{"message":"record already exists","details":[{"type":"request-id","message":"441b524c-1f73-4bc5-9fbf-0e7d563a9a9c"}]}
Creating NS1 Record: cm-stage.portclintonnewsherald.com
Successfully created record for cm-stage.portclintonnewsherald.com
Creating NS1 Record: cm-stage.postcrescent.com
Successfully created record for cm-stage.postcrescent.com
Creating NS1 Record: cm-stage.postsouth.com
::error::Failed to create record: cm-stage.postsouth.com
{"message":"record already exists","details":[{"type":"request-id","message":"69e86be8-f73b-4a45-91a6-e3d542c7b710"}]}
Creating NS1 Record: cm-stage.poughkeepsiejournal.com
Successfully created record for cm-stage.poughkeepsiejournal.com
Creating NS1 Record: cm-stage.press-citizen.com
Successfully created record for cm-stage.press-citizen.com
Creating NS1 Record: cm-stage.pressargus.com
::error::Failed to create record: cm-stage.pressargus.com
{"message":"record already exists","details":[{"type":"request-id","message":"4bd89366-1e41-46a6-b226-093809827dc9"}]}
Creating NS1 Record: cm-stage.pressconnects.com
Successfully created record for cm-stage.pressconnects.com
Creating NS1 Record: cm-stage.progress-index.com
::error::Failed to create record: cm-stage.progress-index.com
{"message":"record already exists","details":[{"type":"request-id","message":"61f694f7-6cff-4d3c-b7a4-f1cf543e2cae"}]}
Creating NS1 Record: cm-stage.providencejournal.com
::error::Failed to create record: cm-stage.providencejournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"2180468d-7458-4256-b86d-0b4344228500"}]}
Creating NS1 Record: cm-stage.publicopiniononline.com
Successfully created record for cm-stage.publicopiniononline.com
Creating NS1 Record: cm-stage.record-courier.com
::error::Failed to create record: cm-stage.record-courier.com
{"message":"record already exists","details":[{"type":"request-id","message":"0a427e52-34d0-4671-9766-da2333838dc7"}]}
Creating NS1 Record: cm-stage.recordnet.com
::error::Failed to create record: cm-stage.recordnet.com
{"message":"record already exists","details":[{"type":"request-id","message":"ba8c611d-b9c9-42b9-a3e0-71bb07df8df8"}]}
Creating NS1 Record: cm-stage.recordonline.com
::error::Failed to create record: cm-stage.recordonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"d415d3bf-da41-4d26-82ff-80ec67bbdaf4"}]}
Creating NS1 Record: cm-stage.recordstar.com
::error::Failed to create record: cm-stage.recordstar.com
{"message":"record already exists","details":[{"type":"request-id","message":"0104f514-3ff0-4598-a9b7-ed1cf739562d"}]}
Creating NS1 Record: cm-stage.redding.com
Successfully created record for cm-stage.redding.com
Creating NS1 Record: cm-stage.registerguard.com
::error::Failed to create record: cm-stage.registerguard.com
{"message":"record already exists","details":[{"type":"request-id","message":"6e680bb9-eb1f-4fd4-b47b-f8dfbb3a7940"}]}
Creating NS1 Record: cm-stage.reporter-times.com
::error::Failed to create record: cm-stage.reporter-times.com
{"message":"record already exists","details":[{"type":"request-id","message":"d436f5f9-5ac7-4b12-8cd3-a045dc8579a2"}]}
Creating NS1 Record: cm-stage.reporternews.com
Successfully created record for cm-stage.reporternews.com
Creating NS1 Record: cm-stage.reviewatlas.com
::error::Failed to create record: cm-stage.reviewatlas.com
{"message":"record already exists","details":[{"type":"request-id","message":"485b8177-eee2-4666-9617-fbb534c14921"}]}
Creating NS1 Record: cm-stage.rgj.com
Successfully created record for cm-stage.rgj.com
Creating NS1 Record: cm-stage.rrstar.com
::error::Failed to create record: cm-stage.rrstar.com
{"message":"record already exists","details":[{"type":"request-id","message":"2c781a44-6a74-4f31-a369-7a1a472c716e"}]}
Creating NS1 Record: cm-stage.ruidosonews.com
Successfully created record for cm-stage.ruidosonews.com
Creating NS1 Record: cm-stage.salina.com
::error::Failed to create record: cm-stage.salina.com
{"message":"record already exists","details":[{"type":"request-id","message":"c1c4b045-d5c4-4bd5-b3f8-0e0e528818a0"}]}
Creating NS1 Record: cm-stage.savannahnow.com
::error::Failed to create record: cm-stage.savannahnow.com
{"message":"record already exists","details":[{"type":"request-id","message":"1a188648-a11f-442d-be29-b6061100cf38"}]}
Creating NS1 Record: cm-stage.scsuntimes.com
::error::Failed to create record: cm-stage.scsuntimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"7fe32ed6-ae92-474f-a738-8c3f4d687c92"}]}
Creating NS1 Record: cm-stage.sctimes.com
Successfully created record for cm-stage.sctimes.com
Creating NS1 Record: cm-stage.seacoastonline.com
::error::Failed to create record: cm-stage.seacoastonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"773e3f32-7284-4036-aa2b-d025cdbc964e"}]}
Creating NS1 Record: cm-stage.sentinel-standard.com
::error::Failed to create record: cm-stage.sentinel-standard.com
{"message":"record already exists","details":[{"type":"request-id","message":"f85523a0-7d1b-46b3-9902-32d134c3120d"}]}
Creating NS1 Record: cm-stage.sheboyganpress.com
Successfully created record for cm-stage.sheboyganpress.com
Creating NS1 Record: cm-stage.shelbystar.com
::error::Failed to create record: cm-stage.shelbystar.com
{"message":"record already exists","details":[{"type":"request-id","message":"0a688068-5d17-4a51-ac50-d4a0b9a0dba6"}]}
Creating NS1 Record: cm-stage.shreveporttimes.com
Successfully created record for cm-stage.shreveporttimes.com
Creating NS1 Record: cm-stage.siskiyoudaily.com
::error::Failed to create record: cm-stage.siskiyoudaily.com
{"message":"record already exists","details":[{"type":"request-id","message":"ad817afe-91c5-4d96-b504-8bac9d9c5a29"}]}
Creating NS1 Record: cm-stage.sj-r.com
::error::Failed to create record: cm-stage.sj-r.com
{"message":"record already exists","details":[{"type":"request-id","message":"ce0848f2-6494-4063-856e-03f3ecd349c4"}]}
Creating NS1 Record: cm-stage.sooeveningnews.com
::error::Failed to create record: cm-stage.sooeveningnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"2edcf445-4d3a-4113-9de2-578b91afac42"}]}
Creating NS1 Record: cm-stage.southbendtribune.com
::error::Failed to create record: cm-stage.southbendtribune.com
{"message":"record already exists","details":[{"type":"request-id","message":"d97e7452-a088-48c3-b05b-cd2c5cb3e5e8"}]}
Creating NS1 Record: cm-stage.southcoasttoday.com
::error::Failed to create record: cm-stage.southcoasttoday.com
{"message":"record already exists","details":[{"type":"request-id","message":"47eb6e14-a725-4315-ab81-812aa95a84b4"}]}
Creating NS1 Record: cm-stage.southernkitchen.com
::error::Failed to create record: cm-stage.southernkitchen.com
{"message":"record already exists","details":[{"type":"request-id","message":"97fdcbf8-f3ad-4137-8a55-2cc32f6ac122"}]}
Creating NS1 Record: cm-stage.spencereveningworld.com
::error::Failed to create record: cm-stage.spencereveningworld.com
{"message":"record already exists","details":[{"type":"request-id","message":"ede6d13a-ec65-4374-ab69-685864b78d01"}]}
Creating NS1 Record: cm-stage.starcourier.com
::error::Failed to create record: cm-stage.starcourier.com
{"message":"record already exists","details":[{"type":"request-id","message":"eaca687b-02d5-4a65-bf7b-82ce3be13efb"}]}
Creating NS1 Record: cm-stage.stargazette.com
Successfully created record for cm-stage.stargazette.com
Creating NS1 Record: cm-stage.starnewsonline.com
::error::Failed to create record: cm-stage.starnewsonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"e67dfb0e-1b74-4f18-916c-d2d406506d3e"}]}
Creating NS1 Record: cm-stage.statesman.com
::error::Failed to create record: cm-stage.statesman.com
{"message":"record already exists","details":[{"type":"request-id","message":"8ec937c5-d2d9-4aa1-b0c2-c54c4a660363"}]}
Creating NS1 Record: cm-stage.statesmanjournal.com
::error::Failed to create record: cm-stage.statesmanjournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"1b687202-b14d-4c33-9263-d4ac087210aa"}]}
Creating NS1 Record: cm-stage.staugustine.com
::error::Failed to create record: cm-stage.staugustine.com
{"message":"record already exists","details":[{"type":"request-id","message":"dccc119c-af58-4576-8ba8-ac21b35c47ed"}]}
Creating NS1 Record: cm-stage.stevenspointjournal.com
Successfully created record for cm-stage.stevenspointjournal.com
Creating NS1 Record: cm-stage.storycityherald.com
::error::Failed to create record: cm-stage.storycityherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"6d42f12b-3985-46fb-bf50-8c25d255e706"}]}
Creating NS1 Record: cm-stage.sturgisjournal.com
::error::Failed to create record: cm-stage.sturgisjournal.com
{"message":"record already exists","details":[{"type":"request-id","message":"b164003f-2244-4ece-9435-bb319b3b9d4f"}]}
Creating NS1 Record: cm-stage.sussexcountian.com
::error::Failed to create record: cm-stage.sussexcountian.com
{"message":"record already exists","details":[{"type":"request-id","message":"afcaa01e-40f5-4ca3-9abc-41c3985e5be9"}]}
Creating NS1 Record: cm-stage.swtimes.com
::error::Failed to create record: cm-stage.swtimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"87bc75be-b500-4be7-8f77-7b4d650e4154"}]}
Creating NS1 Record: cm-stage.tallahassee.com
Successfully created record for cm-stage.tallahassee.com
Creating NS1 Record: cm-stage.tauntongazette.com
::error::Failed to create record: cm-stage.tauntongazette.com
{"message":"record already exists","details":[{"type":"request-id","message":"e5081f8b-2767-4ea6-b4ee-78cd3025433a"}]}
Creating NS1 Record: cm-stage.tcpalm.com
Successfully created record for cm-stage.tcpalm.com
Creating NS1 Record: cm-stage.telegram.com
::error::Failed to create record: cm-stage.telegram.com
{"message":"record already exists","details":[{"type":"request-id","message":"b9bf0ea7-0313-44d4-aeac-087cedbcafd8"}]}
Creating NS1 Record: cm-stage.tennessean.com
Successfully created record for cm-stage.tennessean.com
Creating NS1 Record: cm-stage.the-daily-record.com
::error::Failed to create record: cm-stage.the-daily-record.com
{"message":"record already exists","details":[{"type":"request-id","message":"f4cbc5ff-d6a8-4503-bbf2-596af9b18a39"}]}
Creating NS1 Record: cm-stage.the-leader.com
::error::Failed to create record: cm-stage.the-leader.com
{"message":"record already exists","details":[{"type":"request-id","message":"1fc0399e-6986-461a-b660-49d83b9c2c05"}]}
Creating NS1 Record: cm-stage.the-review.com
::error::Failed to create record: cm-stage.the-review.com
{"message":"record already exists","details":[{"type":"request-id","message":"baead796-6cb4-42b2-bbc0-c80a82f4fef5"}]}
Creating NS1 Record: cm-stage.theadvertiser.com
Successfully created record for cm-stage.theadvertiser.com
Creating NS1 Record: cm-stage.thecalifornian.com
Successfully created record for cm-stage.thecalifornian.com
Creating NS1 Record: cm-stage.thedailyjournal.com
Successfully created record for cm-stage.thedailyjournal.com
Creating NS1 Record: cm-stage.thedailyreporter.com
::error::Failed to create record: cm-stage.thedailyreporter.com
{"message":"record already exists","details":[{"type":"request-id","message":"d59d4fa1-d883-4078-afcb-26df55db1b1b"}]}
Creating NS1 Record: cm-stage.thedestinlog.com
::error::Failed to create record: cm-stage.thedestinlog.com
{"message":"record already exists","details":[{"type":"request-id","message":"d2b14842-cc4b-48f2-b075-92d36bba5766"}]}
Creating NS1 Record: cm-stage.thegardnernews.com
::error::Failed to create record: cm-stage.thegardnernews.com
{"message":"record already exists","details":[{"type":"request-id","message":"bafe802e-0e52-4f68-929e-3040ebf641fb"}]}
Creating NS1 Record: cm-stage.thegleaner.com
Successfully created record for cm-stage.thegleaner.com
Creating NS1 Record: cm-stage.thehammontonnews.com
Successfully created record for cm-stage.thehammontonnews.com
Creating NS1 Record: cm-stage.theintell.com
::error::Failed to create record: cm-stage.theintell.com
{"message":"record already exists","details":[{"type":"request-id","message":"0e8bed04-8b94-4a3b-959e-0ebd464c7936"}]}
Creating NS1 Record: cm-stage.theleafchronicle.com
Successfully created record for cm-stage.theleafchronicle.com
Creating NS1 Record: cm-stage.theledger.com
::error::Failed to create record: cm-stage.theledger.com
{"message":"record already exists","details":[{"type":"request-id","message":"0f26a240-9eff-4998-a430-a12e88ef5ec5"}]}
Creating NS1 Record: cm-stage.thenews-messenger.com
Successfully created record for cm-stage.thenews-messenger.com
Creating NS1 Record: cm-stage.thenewsstar.com
Successfully created record for cm-stage.thenewsstar.com
Creating NS1 Record: cm-stage.thenorthwestern.com
Successfully created record for cm-stage.thenorthwestern.com
Creating NS1 Record: cm-stage.theperrychief.com
::error::Failed to create record: cm-stage.theperrychief.com
{"message":"record already exists","details":[{"type":"request-id","message":"0296f6d2-856f-42eb-85ec-8d23489fafa9"}]}
Creating NS1 Record: cm-stage.thepublicopinion.com
::error::Failed to create record: cm-stage.thepublicopinion.com
{"message":"record already exists","details":[{"type":"request-id","message":"60a6eafa-9c15-4f61-95ca-1a1d4309af16"}]}
Creating NS1 Record: cm-stage.therecordherald.com
::error::Failed to create record: cm-stage.therecordherald.com
{"message":"record already exists","details":[{"type":"request-id","message":"f3c3dd41-199e-45ea-b0fe-364d42a4c2b8"}]}
Creating NS1 Record: cm-stage.thespectrum.com
Successfully created record for cm-stage.thespectrum.com
Creating NS1 Record: cm-stage.thestarpress.com
Successfully created record for cm-stage.thestarpress.com
Creating NS1 Record: cm-stage.thesuburbanite.com
::error::Failed to create record: cm-stage.thesuburbanite.com
{"message":"record already exists","details":[{"type":"request-id","message":"26528a4d-50b3-4023-9a64-0567a13b1ba5"}]}
Creating NS1 Record: cm-stage.thetimesherald.com
Successfully created record for cm-stage.thetimesherald.com
Creating NS1 Record: cm-stage.thetowntalk.com
Successfully created record for cm-stage.thetowntalk.com
Creating NS1 Record: cm-stage.thisweeknews.com
::error::Failed to create record: cm-stage.thisweeknews.com
{"message":"record already exists","details":[{"type":"request-id","message":"8e49185e-411c-440f-9f68-35ded8e63768"}]}
Creating NS1 Record: cm-stage.tidesports.com
::error::Failed to create record: cm-stage.tidesports.com
{"message":"record already exists","details":[{"type":"request-id","message":"fb2de3ab-a3cf-4bb7-998b-ee83e7ae6ea2"}]}
Creating NS1 Record: cm-stage.times-gazette.com
::error::Failed to create record: cm-stage.times-gazette.com
{"message":"record already exists","details":[{"type":"request-id","message":"6a01145c-ed86-4f63-9008-2ff660d62a68"}]}
Creating NS1 Record: cm-stage.timesonline.com
::error::Failed to create record: cm-stage.timesonline.com
{"message":"record already exists","details":[{"type":"request-id","message":"a8347005-5478-453d-933b-54647b123743"}]}
Creating NS1 Record: cm-stage.timesrecordnews.com
Successfully created record for cm-stage.timesrecordnews.com
Creating NS1 Record: cm-stage.timesreporter.com
::error::Failed to create record: cm-stage.timesreporter.com
{"message":"record already exists","details":[{"type":"request-id","message":"7e89507e-272c-41e7-aab6-bd78851a0264"}]}
Creating NS1 Record: cm-stage.timestelegram.com
::error::Failed to create record: cm-stage.timestelegram.com
{"message":"record already exists","details":[{"type":"request-id","message":"e56e6ed1-955f-4494-8124-53bef2c68737"}]}
Creating NS1 Record: cm-stage.tmnews.com
::error::Failed to create record: cm-stage.tmnews.com
{"message":"record already exists","details":[{"type":"request-id","message":"536c3140-4a7a-4dcf-8126-c483bfb30400"}]}
Creating NS1 Record: cm-stage.tricountyindependent.com
::error::Failed to create record: cm-stage.tricountyindependent.com
{"message":"record already exists","details":[{"type":"request-id","message":"306c95ed-794b-4832-9d2d-e87a8390a504"}]}
Creating NS1 Record: cm-stage.tricountytimes.com
::error::Failed to create record: cm-stage.tricountytimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"830dbc58-20dc-40a7-ad6a-7f5603bece14"}]}
Creating NS1 Record: cm-stage.tucson.com
Successfully created record for cm-stage.tucson.com
Creating NS1 Record: cm-stage.tuscaloosanews.com
::error::Failed to create record: cm-stage.tuscaloosanews.com
{"message":"record already exists","details":[{"type":"request-id","message":"1edfaed0-5425-44c2-a176-4ed7b5f77895"}]}
Creating NS1 Record: cm-stage.upstateparent.com
Successfully created record for cm-stage.upstateparent.com
Creating NS1 Record: cm-stage.usatoday.com
::error::Failed to create record: cm-stage.usatoday.com
{"message":"record already exists","details":[{"type":"request-id","message":"d21bd4a5-1f84-4fac-bb4c-c3658be8b3ea"}]}
Creating NS1 Record: cm-stage.usatodaynetwork.com
::error::Failed to create record: cm-stage.usatodaynetwork.com
{"message":"record already exists","details":[{"type":"request-id","message":"7f90eb9b-d740-4ca9-b5ba-11bec463c67d"}]}
Creating NS1 Record: cm-stage.usatodaysportsplus.com
::error::Failed to create record: cm-stage.usatodaysportsplus.com
{"message":"record already exists","details":[{"type":"request-id","message":"f84bacde-1db2-4e27-8a7f-67583d965621"}]}
Creating NS1 Record: cm-stage.uticaod.com
::error::Failed to create record: cm-stage.uticaod.com
{"message":"record already exists","details":[{"type":"request-id","message":"cd5be3c9-4c9b-40b3-ba9b-6423e6886f34"}]}
Creating NS1 Record: cm-stage.vcstar.com
Successfully created record for cm-stage.vcstar.com
Creating NS1 Record: cm-stage.visaliatimesdelta.com
Successfully created record for cm-stage.visaliatimesdelta.com
Creating NS1 Record: cm-stage.vvdailypress.com
::error::Failed to create record: cm-stage.vvdailypress.com
{"message":"record already exists","details":[{"type":"request-id","message":"08a945e5-5322-4def-98a3-5097bd5aa59f"}]}
Creating NS1 Record: cm-stage.waltonsun.com
::error::Failed to create record: cm-stage.waltonsun.com
{"message":"record already exists","details":[{"type":"request-id","message":"d1407df1-9b77-456f-9adf-3a16c3f34c34"}]}
Creating NS1 Record: cm-stage.washingtontimesreporter.com
::error::Failed to create record: cm-stage.washingtontimesreporter.com
{"message":"record already exists","details":[{"type":"request-id","message":"98ed9469-26b7-48c8-a074-b8587c6ed294"}]}
Creating NS1 Record: cm-stage.wausaudailyherald.com
Successfully created record for cm-stage.wausaudailyherald.com
Creating NS1 Record: cm-stage.waynepost.com
::error::Failed to create record: cm-stage.waynepost.com
{"message":"record already exists","details":[{"type":"request-id","message":"3b5c3bf9-6739-4ea5-9b88-13e48d0886cd"}]}
Creating NS1 Record: cm-stage.weeklycitizen.com
::error::Failed to create record: cm-stage.weeklycitizen.com
{"message":"record already exists","details":[{"type":"request-id","message":"a5a87873-8aba-4b0b-baea-f69760cbb4f5"}]}
Creating NS1 Record: cm-stage.wellsvilledaily.com
::error::Failed to create record: cm-stage.wellsvilledaily.com
{"message":"record already exists","details":[{"type":"request-id","message":"bcf6347c-d4cb-49ad-8cc3-44ed332e9b99"}]}
Creating NS1 Record: cm-stage.wickedlocal.com
::error::Failed to create record: cm-stage.wickedlocal.com
{"message":"record already exists","details":[{"type":"request-id","message":"b926fe06-a8ae-47cb-a315-c8047cc8d5df"}]}
Creating NS1 Record: cm-stage.wisconsinrapidstribune.com
Successfully created record for cm-stage.wisconsinrapidstribune.com
Creating NS1 Record: cm-stage.wisfarmer.com
Successfully created record for cm-stage.wisfarmer.com
Creating NS1 Record: cm-stage.woodfordtimes.com
::error::Failed to create record: cm-stage.woodfordtimes.com
{"message":"record already exists","details":[{"type":"request-id","message":"af626b6c-4bfe-4d22-bc26-b46cf29cf8d8"}]}
Creating NS1 Record: cm-stage.worcestermag.com
::error::Failed to create record: cm-stage.worcestermag.com
{"message":"record already exists","details":[{"type":"request-id","message":"48982a53-4991-4b8e-9ffc-548b11f511f7"}]}
Creating NS1 Record: cm-stage.ydr.com
Successfully created record for cm-stage.ydr.com
Creating NS1 Record: cm-stage.yorkdispatch.com
Successfully created record for cm-stage.yorkdispatch.com
Creating NS1 Record: cm-stage.zanesvilletimesrecorder.com
Successfully created record for cm-stage.zanesvilletimesrecorder.com
