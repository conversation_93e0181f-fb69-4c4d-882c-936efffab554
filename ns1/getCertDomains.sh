#!/bin/bash

set -e

LIMIT=500
PAGES=5
OUTPUT_DIR="./ns1certs"

mkdir -p "$OUTPUT_DIR"

if [ -f "X_NSONE_Key.txt" ]; then
  X_NSONE_Key=$(cat X_NSONE_Key.txt) # Replace with your NS1 API key
  echo "Using API key from file"
elif [ -z "$X_NSONE_Key" ]; then
  echo "Error: X_NSONE_Key not found in environment or X_NSONE_Key.txt"
  exit 1
fi

# Initialize pagination
AFTER=""
DOMAINS_COUNT=0

cat /dev/null > $OUTPUT_DIR/certdomains.txt

for PAGE in $(seq 1 $PAGES); do
  echo "Getting Doimains page $PAGE of $PAGES..."

  RESPONSE=$(curl -s \
    -H "accept: application/json" \
    -H "X-NSONE-Key: $X_NSONE_Key" \
    "https://api.nsone.net/v1/redirect/certificates?limit=${LIMIT}${AFTER}")

  # Check for API errors
  if echo "$RESPONSE" | jq -e '.error' > /dev/null; then
    echo "API Error: $(echo "$RESPONSE" | jq -r '.message')"
    exit 1
  fi

  echo "$RESPONSE" | jq . > "$OUTPUT_DIR/certdomains-$PAGE.json"
  echo "$RESPONSE" | jq -r '.results[].domain' >> "$OUTPUT_DIR/certdomains.txt"

  AFTERRESP=$(echo "$RESPONSE" | jq -r '.after')

  if [ "$AFTERRESP" == "null" ] || [ -z "$AFTERRESP" ]; then
    echo "No more pages to fetch."
    break
  else
    AFTER="&after=$AFTERRESP"
    echo "after: $AFTER"
  fi

  sleep 0.5
done

echo "All NS1 cert domains downloaded successfully to $OUTPUT_DIR/"