#!/bin/bash


# Usage:
  # ./check-divested.sh zones.txt 

#  Give it a list of zones and it will check everything for a divestiture. 
#  
#  To use this you need a Fastly API key, GIT configured, Kubernetes configured,  and the Fastly CLI installed
#
## Domain  is configured in fastcrypt.  https://github/GannettDigital/fastcrypt/blob/master/configs/production.json
## Domains configured in uscp fastly service.  https://github/GannettDigital/paas-vcl-uscp/blob/master/templater/load/data/domains.json
## DNS entries are in place.  https://github/GannettDigital/sre-utils/blob/master/NS1/subdomains_for_new_zones.txt
##  or  https://api.fastly/service/2DBSNKsuZbwkzqROGFA68m/version/721/domain

#git clone https://github/GannettDigital/fastcrypt.git
#git clone https://github/GannettDigital/paas-vcl-uscp.git

RED='\033[0;31m'
NC='\033[0m' # No Color

#####  Get current USCP Fastly version to put it in a variable ####
echo "Getting uscp Fastly domains"
fastly domain list --service-id=2DBSNKsuZbwkzqROGFA68m --version=active > uscp-production.txt

echo " Getting EU Fastly domains"
fastly domain list --service-id=keVwwqBMZCUZORPwrMhJG --version=active > eu-production.txt

echo " Getting UWAMP Fastly domains"
fastly domain list --service-id=6zpSWkUXe6ACsGf4tmJ1Oc --version=active > uwamp-production.txt


if [ -d "paas-vcl-uscp" ]; then
  # Control will enter here if $DIRECTORY exists.
  cd paas-vcl-uscp
  git pull
  cd ../
else
  git clone https://github.com/GannettDigital/paas-vcl-uscp.git
fi
if [ -d "fastcrypt" ]; then
  # Control will enter here if $DIRECTORY exists.
  cd fastcrypt
  git pull
  cd ../
else 
  git clone https://github.com/GannettDigital/fastcrypt.git
fi
if [ -d "EdgeRedirects" ]; then
  # Control will enter here if $DIRECTORY exists.
  cd EdgeRedirects
  git pull
  cd ../
else 
  git clone https://github.com/GannettDigital/EdgeRedirects.git
fi


echo "-----   Getting cert information from Fastly..."
./get-tls-certificates.sh
./get-tls-activations.sh

##  Start checking run #

for D in `cat $1`

do

S=`cut -d. -f1 <<< $D`
echo " $S"

##
echo ""
echo  "--------------Checking if $D is configured in uscp-vcl: ------------------  $D  -----"
echo ""
#
if grep $S paas-vcl-uscp/templater/load/data/domains.json 
then
printf "${RED}  YES $D is configured USCP-vcl, delete it${NC} \n"
else
printf " $D is Not configured in USCP-vcl${NC} \n"
fi

echo ""

echo ""
if grep $D uscp-production.txt
then
printf "${RED}  YES $D is configured in Fastly delete it${NC} \n"
else
printf "No $D is Not configured in fastly${NC} \n"
fi
echo "-----"

echo ""
if grep $D eu-production.txt
then
printf "${RED}  YES $D is configured in EU delete it${NC} \n"
else
printf "No $D is Not configured in EU${NC} \n"
fi
echo "-----"

echo ""
if grep $D uwamp-production.txt
then
printf "${RED}  YES $D is configured in UWAMP delete it${NC} \n"
else
printf "No $D is Not configured in UWAMP${NC} \n"
fi
echo "-----"

echo "Checking if $D is in Fastcrypt:  ------"
echo ""
if grep -q -A4 $D fastcrypt/configs/production.json
then
printf " ${RED}  YES $D is configured in Fastcrypt,Delete it${NC} \n"
else
printf " $D is Not configured in Fastcrypt${NC} \n"
fi
echo ""

echo "Checking if $D is in Tangent Fastcrypt:  ------"
echo ""
if grep -q -A4 $D fastcrypt/configs/tangent.json
then
printf " ${RED} YES $D is configured in Tangent Fastcrypt${NC} \n"
else
printf " $D is Not configured in Fastcrypt${NC} \n"
fi
echo ""

echo "Checking if $D is in EdgeRedirects:  ------"
echo ""
if grep -qr $D EdgeRedirects/*
then
printf " ${RED} YES $D is configured in EdgeRedirects.  Delete it${NC} \n"
else
printf "  $D is Not configured in EdgeRedirects${NC} \n"
fi
echo ""

echo "Checking to see if the domain is activated in fastly TLS ------------"
echo ""
if grep -q $D activation*
then
echo "  ${RED} YES $D found in Fastly cert activations ${NC}"
else
printf " $D does NOT appear to be Activated in Fastly, Check Manually \n"
fi
echo ""
#---
printf "Check to see if there's a cert in fastly:  --- ${RED} \n"
if grep $D certs*
then
printf "  $D found In Fastly ---${NC}"
else
printf " ${NC} $D is Not in Certs in Fastly, Check Manually ${NC}  \n"
fi
echo ""
echo "---  Check cert in Kubernetes  --"
kubectl get cert $D -oyaml |grep notAfter
#---
echo
echo  "Done with config checks --"
echo ""
echo  "  -----  DNS check  ---"
echo  "Checking www :  ---"
host www.$D
echo  "Checking user :  ---"
host user.$D
echo "---"
whois $D | grep "Name Server"
echo ""

#  if $2 is yes we check all the dns #


done

